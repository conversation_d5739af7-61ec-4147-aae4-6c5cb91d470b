@import "tailwindcss";

/* <PERSON>ont */
@font-face {
  font-family: '<PERSON>';
  src: url('/webfonts/Cooper-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>';
  src: url('/webfonts/Cooper-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cooper';
  src: url('/webfonts/Cooper-SemiBold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cooper';
  src: url('/webfonts/Cooper-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Cooper';
  src: url('/webfonts/Cooper-ExtraBold.woff2') format('woff2');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: '<PERSON>';
  src: url('/webfonts/Cooper-Black.woff2') format('woff2');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  
  --primary: #4F46E5;
  --primary-rgb: 79, 70, 229;
  --primary-foreground: #ffffff;
  
  --secondary: #6D28D9;
  --secondary-rgb: 109, 40, 217;
  --secondary-foreground: #ffffff;
  
  --accent: #F9FAFB;
  --accent-foreground: #111827;
  
  --muted: #F3F4F6;
  --muted-foreground: #6B7280;
  
  --destructive: #EF4444;
  --destructive-foreground: #ffffff;
  
  --card: #ffffff;
  --card-foreground: #171717;
  
  --border: #E5E7EB;
  --input: #E5E7EB;
  
  --radius: 0.5rem;
  
  /* Add Cooper font family to the root variables */
  --font-cooper: var(--font-inter), 'Inter', sans-serif;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-inter: var(--font-inter);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    
    --primary: #6366F1;
    --primary-rgb: 99, 102, 241;
    --primary-foreground: #ffffff;
    
    --secondary: #8B5CF6;
    --secondary-rgb: 139, 92, 246;
    --secondary-foreground: #ffffff;
    
    --accent: #1F2937;
    --accent-foreground: #F9FAFB;
    
    --muted: #1F2937;
    --muted-foreground: #9CA3AF;
    
    --destructive: #F87171;
    --destructive-foreground: #ffffff;
    
    --card: #111827;
    --card-foreground: #F9FAFB;
    
    --border: #374151;
    --input: #374151;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Gradient Text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-purple-indigo {
  background-image: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
}

.gradient-blue-purple {
  background-image: linear-gradient(135deg, #3B82F6 0%, #8B5CF6 100%);
}

.gradient-green-blue {
  background-image: linear-gradient(135deg, #10B981 0%, #3B82F6 100%);
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --card-foreground: 210 40% 98%;
  --card-background: 222.2 84% 4.9%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 47.4% 11.2%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  
  --radius: 0.5rem;
}

/* Global Styles */
body {
  @apply bg-black text-white;
}

@layer utilities {
  .animate-slow-float {
    animation: float 4s ease-in-out infinite;
  }
  
  .perspective-800 {
    perspective: 800px;
  }
  
  .transform-gpu {
    transform: translateZ(0);
    will-change: transform;
  }
  
  .rotate-y-15 { transform: rotateY(15deg); }
  .rotate-y-25 { transform: rotateY(25deg); }
  .rotate-y-30 { transform: rotateY(30deg); }
  .rotate-y-45 { transform: rotateY(45deg); }
  .rotate-y-90 { transform: rotateY(90deg); }
  
  .rotate-x-5 { transform: rotateX(5deg); }
  .rotate-x-12 { transform: rotateX(12deg); }
  .rotate-x-15 { transform: rotateX(15deg); }
  .rotate-x-45 { transform: rotateX(45deg); }
  .rotate-x-90 { transform: rotateX(90deg); }
  
  .rotate-z-45 { transform: rotateZ(45deg); }
  .-rotate-z-20 { transform: rotateZ(-20deg); }
  
  .translate-z-1 { transform: translateZ(1px); }
  .translate-z-2 { transform: translateZ(2px); }
  .translate-z-3 { transform: translateZ(3px); }
  .translate-z-6 { transform: translateZ(6px); }
}

@keyframes float {
  0% {
    transform: translate3d(0px, 0px, 0px) rotateX(15deg) rotateY(15deg);
  }
  50% {
    transform: translate3d(0px, 10px, 0px) rotateX(25deg) rotateY(25deg);
  }
  100% {
    transform: translate3d(0px, 0px, 0px) rotateX(15deg) rotateY(15deg);
  }
}

/* Three.js specific styles */
.three-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* Make feature cards pop */
.feature-card-3d {
  transform: translateZ(0);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card-3d:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04),
              0 0 20px 0px rgba(255, 62, 136, 0.2);
}