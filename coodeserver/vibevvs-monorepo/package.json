{"name": "vibevvs-monorepo", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "build:packages": "turbo run build --filter='./packages/*'", "build:app": "if [ -z \"$APP_NAME\" ]; then echo 'Error: APP_NAME environment variable is not set.' && exit 1; else turbo run build --filter=\"./apps/$APP_NAME\"; fi", "build:all-for-app": "npm run build:packages && npm run build:app", "dev": "turbo run dev", "lint": "turbo run lint", "start": "if [ \"$APP_NAME\" = \"web\" ]; then cd apps/web && npm start; elif [ \"$APP_NAME\" = \"ws-server\" ]; then cd apps/ws-server && node dist/index.js; else cd apps/web && npm start; fi", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "test": "turbo run test"}, "devDependencies": {"@types/uuid": "^10.0.0", "eslint": "^8.56.0", "prettier": "^3.1.1", "turbo": "^1.12.5"}, "engines": {"node": "18.x"}, "packageManager": "npm@10.2.3", "dependencies": {"@clerk/backend": "^1.31.4", "@google/generative-ai": "^0.24.1", "@iconify/react": "^6.0.0", "@lobehub/icons-static-svg": "^1.47.0", "clsx": "^2.1.1", "eslint-config-next": "^15.3.2", "framer-motion": "^12.12.1", "http-proxy": "^1.18.1", "motion": "^12.12.1", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "simple-icons": "^14.14.0", "tailwind-merge": "^3.3.0"}}