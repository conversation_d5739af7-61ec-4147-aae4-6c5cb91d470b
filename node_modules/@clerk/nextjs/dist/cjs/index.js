"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var index_exports = {};
__export(index_exports, {
  AuthenticateWithRedirectCallback: () => import_controlComponents.AuthenticateWithRedirectCallback,
  ClerkDegraded: () => import_controlComponents.ClerkDegraded,
  ClerkFailed: () => import_controlComponents.ClerkFailed,
  ClerkLoaded: () => import_controlComponents.ClerkLoaded,
  ClerkLoading: () => import_controlComponents.ClerkLoading,
  ClerkProvider: () => ClerkProvider,
  CreateOrganization: () => import_uiComponents.CreateOrganization,
  GoogleOneTap: () => import_uiComponents.GoogleOneTap,
  OrganizationList: () => import_uiComponents.OrganizationList,
  OrganizationProfile: () => import_uiComponents.OrganizationProfile,
  OrganizationSwitcher: () => import_uiComponents.OrganizationSwitcher,
  PricingTable: () => import_uiComponents.PricingTable,
  Protect: () => Protect,
  RedirectToCreateOrganization: () => import_controlComponents.RedirectToCreateOrganization,
  RedirectToOrganizationProfile: () => import_controlComponents.RedirectToOrganizationProfile,
  RedirectToSignIn: () => import_controlComponents.RedirectToSignIn,
  RedirectToSignUp: () => import_controlComponents.RedirectToSignUp,
  RedirectToUserProfile: () => import_controlComponents.RedirectToUserProfile,
  SignIn: () => import_uiComponents.SignIn,
  SignInButton: () => import_uiComponents.SignInButton,
  SignInWithMetamaskButton: () => import_uiComponents.SignInWithMetamaskButton,
  SignOutButton: () => import_uiComponents.SignOutButton,
  SignUp: () => import_uiComponents.SignUp,
  SignUpButton: () => import_uiComponents.SignUpButton,
  SignedIn: () => SignedIn,
  SignedOut: () => SignedOut,
  UserButton: () => import_uiComponents.UserButton,
  UserProfile: () => import_uiComponents.UserProfile,
  Waitlist: () => import_uiComponents.Waitlist,
  useAuth: () => import_hooks.useAuth,
  useClerk: () => import_hooks.useClerk,
  useEmailLink: () => import_hooks.useEmailLink,
  useOrganization: () => import_hooks.useOrganization,
  useOrganizationList: () => import_hooks.useOrganizationList,
  useReverification: () => import_hooks.useReverification,
  useSession: () => import_hooks.useSession,
  useSessionList: () => import_hooks.useSessionList,
  useSignIn: () => import_hooks.useSignIn,
  useSignUp: () => import_hooks.useSignUp,
  useUser: () => import_hooks.useUser
});
module.exports = __toCommonJS(index_exports);
var import_controlComponents = require("./client-boundary/controlComponents");
var import_uiComponents = require("./client-boundary/uiComponents");
var import_hooks = require("./client-boundary/hooks");
var ComponentsModule = __toESM(require("#components"));
const ClerkProvider = ComponentsModule.ClerkProvider;
const SignedIn = ComponentsModule.SignedIn;
const SignedOut = ComponentsModule.SignedOut;
const Protect = ComponentsModule.Protect;
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  AuthenticateWithRedirectCallback,
  ClerkDegraded,
  ClerkFailed,
  ClerkLoaded,
  ClerkLoading,
  ClerkProvider,
  CreateOrganization,
  GoogleOneTap,
  OrganizationList,
  OrganizationProfile,
  OrganizationSwitcher,
  PricingTable,
  Protect,
  RedirectToCreateOrganization,
  RedirectToOrganizationProfile,
  RedirectToSignIn,
  RedirectToSignUp,
  RedirectToUserProfile,
  SignIn,
  SignInButton,
  SignInWithMetamaskButton,
  SignOutButton,
  SignUp,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
  UserProfile,
  Waitlist,
  useAuth,
  useClerk,
  useEmailLink,
  useOrganization,
  useOrganizationList,
  useReverification,
  useSession,
  useSessionList,
  useSignIn,
  useSignUp,
  useUser
});
//# sourceMappingURL=index.js.map