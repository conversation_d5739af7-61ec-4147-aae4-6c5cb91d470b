{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  AuthenticateWithRedirect<PERSON>allback,\n  ClerkLoa<PERSON>,\n  <PERSON>Loa<PERSON>,\n  <PERSON>D<PERSON>raded,\n  ClerkFailed,\n  RedirectToCreateOrganization,\n  RedirectToOrganizationProfile,\n  RedirectToSignIn,\n  RedirectToSignUp,\n  RedirectToUserProfile,\n} from './client-boundary/controlComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  CreateOrganization,\n  OrganizationList,\n  OrganizationProfile,\n  OrganizationSwitcher,\n  SignIn,\n  SignInButton,\n  SignInWithMetamaskButton,\n  SignOutButton,\n  SignUp,\n  SignUpButton,\n  UserButton,\n  UserProfile,\n  GoogleOneTap,\n  Waitlist,\n  PricingTable,\n} from './client-boundary/uiComponents';\n\n/**\n * These need to be explicitly listed. Do not use an * here.\n * If you do, app router will break.\n */\nexport {\n  useAuth,\n  useClerk,\n  useEmailLink,\n  useOrganization,\n  useOrganizationList,\n  useSession,\n  useSessionList,\n  useSignIn,\n  useSignUp,\n  useUser,\n  useReverification,\n} from './client-boundary/hooks';\n\n/**\n * Conditionally export components that exhibit different behavior\n * when used in /app vs /pages.\n * We defined the runtime and the type values explicitly,\n * because TS will not recognize the subpath import unless the HOST\n * application sets moduleResolution to 'NodeNext'.\n */\n// @ts-ignore\nimport * as ComponentsModule from '#components';\n\nimport type { ServerComponentsServerModuleTypes } from './components.server';\n\nexport const ClerkProvider = ComponentsModule.ClerkProvider as ServerComponentsServerModuleTypes['ClerkProvider'];\nexport const SignedIn = ComponentsModule.SignedIn as ServerComponentsServerModuleTypes['SignedIn'];\nexport const SignedOut = ComponentsModule.SignedOut as ServerComponentsServerModuleTypes['SignedOut'];\nexport const Protect = ComponentsModule.Protect as ServerComponentsServerModuleTypes['Protect'];\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,+BAWO;AAMP,0BAgBO;AAMP,mBAYO;AAUP,uBAAkC;AAI3B,MAAM,gBAAgB,iBAAiB;AACvC,MAAM,WAAW,iBAAiB;AAClC,MAAM,YAAY,iBAAiB;AACnC,MAAM,UAAU,iBAAiB;", "names": []}