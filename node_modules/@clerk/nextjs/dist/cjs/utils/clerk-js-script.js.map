{"version": 3, "sources": ["../../../src/utils/clerk-js-script.tsx"], "sourcesContent": ["import { useClerk } from '@clerk/clerk-react';\nimport { buildClerkJsScriptAttributes, clerkJsScriptUrl } from '@clerk/clerk-react/internal';\nimport NextScript from 'next/script';\nimport React from 'react';\n\nimport { useClerkNextOptions } from '../client-boundary/NextOptionsContext';\n\ntype ClerkJSScriptProps = {\n  router: 'app' | 'pages';\n};\n\nfunction ClerkJSScript(props: ClerkJSScriptProps) {\n  const { publishable<PERSON>ey, clerkJSU<PERSON>, clerkJ<PERSON><PERSON><PERSON>, clerkJSV<PERSON>t, nonce } = useClerkNextOptions();\n  const { domain, proxyUrl } = useClerk();\n\n  /**\n   * If no publishable key, avoid appending an invalid script in the DOM.\n   */\n  if (!publishableKey) {\n    return null;\n  }\n\n  const options = {\n    domain,\n    proxyUrl,\n    publishable<PERSON><PERSON>,\n    clerkJ<PERSON>rl,\n    clerkJSVersion,\n    clerkJSV<PERSON>t,\n    nonce,\n  };\n  const scriptUrl = clerkJsScriptUrl(options);\n\n  /**\n   * Notes:\n   * `next/script` in 13.x.x when used with App Router will fail to pass any of our `data-*` attributes, resulting in errors\n   * Nextjs App Router will automatically move inline scripts inside `<head/>`\n   * Using the `nextjs/script` for App Router with the `beforeInteractive` strategy will throw an error because our custom script will be mounted outside the `html` tag.\n   */\n  const Script = props.router === 'app' ? 'script' : NextScript;\n\n  return (\n    <Script\n      src={scriptUrl}\n      data-clerk-js-script\n      async\n      // `nextjs/script` will add defer by default and does not get removed when we async is true\n      defer={props.router === 'pages' ? false : undefined}\n      crossOrigin='anonymous'\n      strategy={props.router === 'pages' ? 'beforeInteractive' : undefined}\n      {...buildClerkJsScriptAttributes(options)}\n    />\n  );\n}\n\nexport { ClerkJSScript };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,yBAAyB;AACzB,sBAA+D;AAC/D,oBAAuB;AACvB,mBAAkB;AAElB,gCAAoC;AAMpC,SAAS,cAAc,OAA2B;AAChD,QAAM,EAAE,gBAAgB,YAAY,gBAAgB,gBAAgB,MAAM,QAAI,+CAAoB;AAClG,QAAM,EAAE,QAAQ,SAAS,QAAI,6BAAS;AAKtC,MAAI,CAAC,gBAAgB;AACnB,WAAO;AAAA,EACT;AAEA,QAAM,UAAU;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACA,QAAM,gBAAY,kCAAiB,OAAO;AAQ1C,QAAM,SAAS,MAAM,WAAW,QAAQ,WAAW,cAAAA;AAEnD,SACE,6BAAAC,QAAA;AAAA,IAAC;AAAA;AAAA,MACC,KAAK;AAAA,MACL,wBAAoB;AAAA,MACpB,OAAK;AAAA,MAEL,OAAO,MAAM,WAAW,UAAU,QAAQ;AAAA,MAC1C,aAAY;AAAA,MACZ,UAAU,MAAM,WAAW,UAAU,sBAAsB;AAAA,MAC1D,OAAG,8CAA6B,OAAO;AAAA;AAAA,EAC1C;AAEJ;", "names": ["NextScript", "React"]}