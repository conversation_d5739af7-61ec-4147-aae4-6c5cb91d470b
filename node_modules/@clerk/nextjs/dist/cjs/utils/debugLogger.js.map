{"version": 3, "sources": ["../../../src/utils/debugLogger.ts"], "sourcesContent": ["// TODO: Replace with a more sophisticated logging solution\n\nimport nextPkg from 'next/package.json';\n\nimport { logFormatter } from './logFormatter';\n\nexport type Log = string | Record<string, unknown>;\nexport type LogEntry = Log | Log[];\nexport type Logger<L = Log> = {\n  commit: () => void;\n  debug: (...args: Array<L | (() => L)>) => void;\n  enable: () => void;\n};\nexport type LoggerNoCommit<L = Logger> = Omit<L, 'commit'>;\n\nexport const createDebugLogger = (name: string, formatter: (val: LogEntry) => string) => (): Logger => {\n  const entries: LogEntry[] = [];\n  let isEnabled = false;\n\n  return {\n    enable: () => {\n      isEnabled = true;\n    },\n    debug: (...args) => {\n      if (isEnabled) {\n        entries.push(args.map(arg => (typeof arg === 'function' ? arg() : arg)));\n      }\n    },\n    commit: () => {\n      if (isEnabled) {\n        console.log(debugLogHeader(name));\n\n        /**\n         * We buffer each collected log entry so we can format them and log them all at once.\n         * Individual console.log calls are used to ensure we don't hit platform-specific log limits (Vercel and Netlify are 4kb).\n         */\n        for (const log of entries) {\n          let output = formatter(log);\n\n          output = output\n            .split('\\n')\n            .map(l => `  ${l}`)\n            .join('\\n');\n\n          // Vercel errors if the output is > 4kb, so we truncate it\n          if (process.env.VERCEL) {\n            output = truncate(output, 4096);\n          }\n\n          console.log(output);\n        }\n\n        console.log(debugLogFooter(name));\n      }\n    },\n  };\n};\n\ntype WithLogger = <L extends Logger, H extends (...args: any[]) => any>(\n  loggerFactoryOrName: string | (() => L),\n  handlerCtor: (logger: LoggerNoCommit<L>) => H,\n) => H;\n\nexport const withLogger: WithLogger = (loggerFactoryOrName, handlerCtor) => {\n  return ((...args: any) => {\n    const factory =\n      typeof loggerFactoryOrName === 'string'\n        ? createDebugLogger(loggerFactoryOrName, logFormatter)\n        : loggerFactoryOrName;\n\n    const logger = factory();\n    const handler = handlerCtor(logger as any);\n\n    try {\n      const res = handler(...args);\n      if (typeof res === 'object' && 'then' in res && typeof res.then === 'function') {\n        return res\n          .then((val: any) => {\n            logger.commit();\n            return val;\n          })\n          .catch((err: any) => {\n            logger.commit();\n            throw err;\n          });\n      }\n      // handle sync methods\n      logger.commit();\n      return res;\n    } catch (err: any) {\n      logger.commit();\n      throw err;\n    }\n  }) as ReturnType<typeof handlerCtor>;\n};\n\nfunction debugLogHeader(name: string) {\n  return `[clerk debug start: ${name}]`;\n}\n\nfunction debugLogFooter(name: string) {\n  return `[clerk debug end: ${name}] (@clerk/nextjs=${PACKAGE_VERSION},next=${nextPkg.version},timestamp=${Math.round(new Date().getTime() / 1_000)})`;\n}\n\n// ref: https://stackoverflow.com/questions/57769465/javascript-truncate-text-by-bytes-length\nfunction truncate(str: string, maxLength: number) {\n  const encoder = new TextEncoder();\n  const decoder = new TextDecoder('utf-8');\n\n  const encodedString = encoder.encode(str);\n  const truncatedString = encodedString.slice(0, maxLength);\n\n  // return the truncated string, removing any replacement characters that result from partially truncated characters\n  return decoder.decode(truncatedString).replace(/\\uFFFD/g, '');\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,qBAAoB;AAEpB,0BAA6B;AAWtB,MAAM,oBAAoB,CAAC,MAAc,cAAyC,MAAc;AACrG,QAAM,UAAsB,CAAC;AAC7B,MAAI,YAAY;AAEhB,SAAO;AAAA,IACL,QAAQ,MAAM;AACZ,kBAAY;AAAA,IACd;AAAA,IACA,OAAO,IAAI,SAAS;AAClB,UAAI,WAAW;AACb,gBAAQ,KAAK,KAAK,IAAI,SAAQ,OAAO,QAAQ,aAAa,IAAI,IAAI,GAAI,CAAC;AAAA,MACzE;AAAA,IACF;AAAA,IACA,QAAQ,MAAM;AACZ,UAAI,WAAW;AACb,gBAAQ,IAAI,eAAe,IAAI,CAAC;AAMhC,mBAAW,OAAO,SAAS;AACzB,cAAI,SAAS,UAAU,GAAG;AAE1B,mBAAS,OACN,MAAM,IAAI,EACV,IAAI,OAAK,KAAK,CAAC,EAAE,EACjB,KAAK,IAAI;AAGZ,cAAI,QAAQ,IAAI,QAAQ;AACtB,qBAAS,SAAS,QAAQ,IAAI;AAAA,UAChC;AAEA,kBAAQ,IAAI,MAAM;AAAA,QACpB;AAEA,gBAAQ,IAAI,eAAe,IAAI,CAAC;AAAA,MAClC;AAAA,IACF;AAAA,EACF;AACF;AAOO,MAAM,aAAyB,CAAC,qBAAqB,gBAAgB;AAC1E,SAAQ,IAAI,SAAc;AACxB,UAAM,UACJ,OAAO,wBAAwB,WAC3B,kBAAkB,qBAAqB,gCAAY,IACnD;AAEN,UAAM,SAAS,QAAQ;AACvB,UAAM,UAAU,YAAY,MAAa;AAEzC,QAAI;AACF,YAAM,MAAM,QAAQ,GAAG,IAAI;AAC3B,UAAI,OAAO,QAAQ,YAAY,UAAU,OAAO,OAAO,IAAI,SAAS,YAAY;AAC9E,eAAO,IACJ,KAAK,CAAC,QAAa;AAClB,iBAAO,OAAO;AACd,iBAAO;AAAA,QACT,CAAC,EACA,MAAM,CAAC,QAAa;AACnB,iBAAO,OAAO;AACd,gBAAM;AAAA,QACR,CAAC;AAAA,MACL;AAEA,aAAO,OAAO;AACd,aAAO;AAAA,IACT,SAAS,KAAU;AACjB,aAAO,OAAO;AACd,YAAM;AAAA,IACR;AAAA,EACF;AACF;AAEA,SAAS,eAAe,MAAc;AACpC,SAAO,uBAAuB,IAAI;AACpC;AAEA,SAAS,eAAe,MAAc;AACpC,SAAO,qBAAqB,IAAI,oBAAoB,QAAe,SAAS,eAAAA,QAAQ,OAAO,cAAc,KAAK,OAAM,oBAAI,KAAK,GAAE,QAAQ,IAAI,GAAK,CAAC;AACnJ;AAGA,SAAS,SAAS,KAAa,WAAmB;AAChD,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,UAAU,IAAI,YAAY,OAAO;AAEvC,QAAM,gBAAgB,QAAQ,OAAO,GAAG;AACxC,QAAM,kBAAkB,cAAc,MAAM,GAAG,SAAS;AAGxD,SAAO,QAAQ,OAAO,eAAe,EAAE,QAAQ,WAAW,EAAE;AAC9D;", "names": ["nextPkg"]}