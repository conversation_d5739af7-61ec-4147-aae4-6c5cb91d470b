"use client";
import "../../chunk-BUSYA2B4.js";
import { <PERSON><PERSON><PERSON><PERSON> as ReactClerkProvider } from "@clerk/clerk-react";
import { inBrowser } from "@clerk/shared/browser";
import { logger } from "@clerk/shared/logger";
import dynamic from "next/dynamic";
import { useRouter } from "next/navigation";
import nextPackage from "next/package.json";
import React, { useEffect, useTransition } from "react";
import { useSafeLayoutEffect } from "../../client-boundary/hooks/useSafeLayoutEffect";
import { ClerkNextOptionsProvider, useClerkNextOptions } from "../../client-boundary/NextOptionsContext";
import { ClerkJSScript } from "../../utils/clerk-js-script";
import { canUseKeyless } from "../../utils/feature-flags";
import { mergeNextClerkPropsWithEnv } from "../../utils/mergeNextClerkPropsWithEnv";
import { RouterTelemetry } from "../../utils/router-telemetry";
import { isNextWithUnstableServerActions } from "../../utils/sdk-versions";
import { invalidateCacheAction } from "../server-actions";
import { useAwaitablePush } from "./useAwaitablePush";
import { useAwaitableReplace } from "./useAwaitableReplace";
const LazyCreateKeylessApplication = dynamic(
  () => import("./keyless-creator-reader.js").then((m) => m.KeylessCreatorOrReader)
);
const NextClientClerkProvider = (props) => {
  if (isNextWithUnstableServerActions) {
    const deprecationWarning = `Clerk:
Your current Next.js version (${nextPackage.version}) will be deprecated in the next major release of "@clerk/nextjs". Please upgrade to next@14.1.0 or later.`;
    if (inBrowser()) {
      logger.warnOnce(deprecationWarning);
    } else {
      logger.logOnce(`
\x1B[43m----------
${deprecationWarning}
----------\x1B[0m
`);
    }
  }
  const { __unstable_invokeMiddlewareOnAuthStateChange = true, children } = props;
  const router = useRouter();
  const push = useAwaitablePush();
  const replace = useAwaitableReplace();
  const [isPending, startTransition] = useTransition();
  const isNested = Boolean(useClerkNextOptions());
  if (isNested) {
    return props.children;
  }
  useEffect(() => {
    var _a;
    if (!isPending) {
      (_a = window.__clerk_internal_invalidateCachePromise) == null ? void 0 : _a.call(window);
    }
  }, [isPending]);
  useSafeLayoutEffect(() => {
    window.__unstable__onBeforeSetActive = (intent) => {
      return new Promise((resolve) => {
        var _a;
        window.__clerk_internal_invalidateCachePromise = resolve;
        const nextVersion = ((_a = window == null ? void 0 : window.next) == null ? void 0 : _a.version) || "";
        if (nextVersion.startsWith("13")) {
          startTransition(() => {
            router.refresh();
          });
        } else if (nextVersion.startsWith("15") && intent === "sign-out") {
          resolve();
        } else {
          void invalidateCacheAction().then(() => resolve());
        }
      });
    };
    window.__unstable__onAfterSetActive = () => {
      if (__unstable_invokeMiddlewareOnAuthStateChange) {
        return router.refresh();
      }
    };
  }, []);
  const mergedProps = mergeNextClerkPropsWithEnv({
    ...props,
    // @ts-expect-error Error because of the stricter types of internal `push`
    routerPush: push,
    // @ts-expect-error Error because of the stricter types of internal `replace`
    routerReplace: replace
  });
  return /* @__PURE__ */ React.createElement(ClerkNextOptionsProvider, { options: mergedProps }, /* @__PURE__ */ React.createElement(ReactClerkProvider, { ...mergedProps }, /* @__PURE__ */ React.createElement(RouterTelemetry, null), /* @__PURE__ */ React.createElement(ClerkJSScript, { router: "app" }), children));
};
const ClientClerkProvider = (props) => {
  const { children, disableKeyless = false, ...rest } = props;
  const safePublishableKey = mergeNextClerkPropsWithEnv(rest).publishableKey;
  if (safePublishableKey || !canUseKeyless || disableKeyless) {
    return /* @__PURE__ */ React.createElement(NextClientClerkProvider, { ...rest }, children);
  }
  return /* @__PURE__ */ React.createElement(LazyCreateKeylessApplication, null, /* @__PURE__ */ React.createElement(NextClientClerkProvider, { ...rest }, children));
};
export {
  ClientClerkProvider
};
//# sourceMappingURL=ClerkProvider.js.map