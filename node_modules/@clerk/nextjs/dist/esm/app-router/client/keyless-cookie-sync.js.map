{"version": 3, "sources": ["../../../../src/app-router/client/keyless-cookie-sync.tsx"], "sourcesContent": ["'use client';\n\nimport type { AccountlessApplication } from '@clerk/backend';\nimport { useSelectedLayoutSegments } from 'next/navigation';\nimport type { PropsWithChildren } from 'react';\nimport { useEffect } from 'react';\n\nimport { canUseKeyless } from '../../utils/feature-flags';\n\nexport function KeylessCookieSync(props: PropsWithChildren<AccountlessApplication>) {\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = segments[0]?.startsWith('/_not-found') || false;\n\n  useEffect(() => {\n    if (canUseKeyless && !isNotFoundRoute) {\n      void import('../keyless-actions.js').then(m =>\n        m.syncKeylessConfigAction({\n          ...props,\n          // Preserve the current url and return back, once keys are synced in the middleware\n          returnUrl: window.location.href,\n        }),\n      );\n    }\n  }, [isNotFoundRoute]);\n\n  return props.children;\n}\n"], "mappings": ";;AAGA,SAAS,iCAAiC;AAE1C,SAAS,iBAAiB;AAE1B,SAAS,qBAAqB;AAEvB,SAAS,kBAAkB,OAAkD;AATpF;AAUE,QAAM,WAAW,0BAA0B;AAC3C,QAAM,oBAAkB,cAAS,CAAC,MAAV,mBAAa,WAAW,mBAAkB;AAElE,YAAU,MAAM;AACd,QAAI,iBAAiB,CAAC,iBAAiB;AACrC,WAAK,OAAO,uBAAuB,EAAE;AAAA,QAAK,OACxC,EAAE,wBAAwB;AAAA,UACxB,GAAG;AAAA;AAAA,UAEH,WAAW,OAAO,SAAS;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF;AAAA,EACF,GAAG,CAAC,eAAe,CAAC;AAEpB,SAAO,MAAM;AACf;", "names": []}