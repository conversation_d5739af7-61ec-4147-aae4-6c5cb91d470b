{"version": 3, "sources": ["../../../../src/app-router/client/useAwaitablePush.ts"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\n\nimport { useInternalNavFun } from './useInternalNavFun';\n\n/**\n * Creates an \"awaitable\" navigation function that will do its best effort to wait for Next.js to finish its route transition.\n * This is accomplished by wrapping the call to `router.push` in `startTransition()`, which should rely on <PERSON>act to coordinate the pending state. We key off of\n * `isPending` to flush the stored promises and ensure the navigates \"resolve\".\n */\nexport const useAwaitablePush = () => {\n  const router = useRouter();\n\n  return useInternalNavFun({\n    windowNav: typeof window !== 'undefined' ? window.history.pushState.bind(window.history) : undefined,\n    routerNav: router.push.bind(router),\n    name: 'push',\n  });\n};\n"], "mappings": ";;AAEA,SAAS,iBAAiB;AAE1B,SAAS,yBAAyB;AAO3B,MAAM,mBAAmB,MAAM;AACpC,QAAM,SAAS,UAAU;AAEzB,SAAO,kBAAkB;AAAA,IACvB,WAAW,OAAO,WAAW,cAAc,OAAO,QAAQ,UAAU,KAAK,OAAO,OAAO,IAAI;AAAA,IAC3F,WAAW,OAAO,KAAK,KAAK,MAAM;AAAA,IAClC,MAAM;AAAA,EACR,CAAC;AACH;", "names": []}