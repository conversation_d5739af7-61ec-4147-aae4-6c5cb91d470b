{"version": 3, "sources": ["../../../../src/app-router/server/ClerkProvider.tsx"], "sourcesContent": ["import type { InitialState, Without } from '@clerk/types';\nimport { headers } from 'next/headers';\nimport type { ReactNode } from 'react';\nimport React from 'react';\n\nimport { PromisifiedAuthProvider } from '../../client-boundary/PromisifiedAuthProvider';\nimport { getDynamicAuthData } from '../../server/buildClerkProps';\nimport type { NextClerkProviderProps } from '../../types';\nimport { mergeNextClerkPropsWithEnv } from '../../utils/mergeNextClerkPropsWithEnv';\nimport { isNext13 } from '../../utils/sdk-versions';\nimport { ClientClerkProvider } from '../client/ClerkProvider';\nimport { getKeylessStatus, KeylessProvider } from './keyless-provider';\nimport { buildRequestLike, getScriptNonceFromHeader } from './utils';\n\nconst getDynamicClerkState = React.cache(async function getDynamicClerkState() {\n  const request = await buildRequestLike();\n  const data = getDynamicAuthData(request);\n\n  return data;\n});\n\nconst getNonceHeaders = React.cache(async function getNonceHeaders() {\n  const headersList = await headers();\n  const nonce = headersList.get('X-Nonce');\n  return nonce\n    ? nonce\n    : // Fallback to extracting from CSP header\n      getScriptNonceFromHeader(headersList.get('Content-Security-Policy') || '') || '';\n});\n\nexport async function ClerkProvider(\n  props: Without<NextClerkProviderProps, '__unstable_invokeMiddlewareOnAuthStateChange'>,\n) {\n  const { children, dynamic, ...rest } = props;\n\n  async function generateStatePromise() {\n    if (!dynamic) {\n      return Promise.resolve(null);\n    }\n    if (isNext13) {\n      /**\n       * For some reason, Next 13 requires that functions which call `headers()` are awaited where they are invoked.\n       * Without the await here, Next will throw a DynamicServerError during build.\n       */\n      return Promise.resolve(await getDynamicClerkState());\n    }\n    return getDynamicClerkState();\n  }\n\n  async function generateNonce() {\n    if (!dynamic) {\n      return Promise.resolve('');\n    }\n    if (isNext13) {\n      /**\n       * For some reason, Next 13 requires that functions which call `headers()` are awaited where they are invoked.\n       * Without the await here, Next will throw a DynamicServerError during build.\n       */\n      return Promise.resolve(await getNonceHeaders());\n    }\n    return getNonceHeaders();\n  }\n\n  const propsWithEnvs = mergeNextClerkPropsWithEnv({\n    ...rest,\n  });\n\n  const { shouldRunAsKeyless, runningWithClaimedKeys } = await getKeylessStatus(propsWithEnvs);\n\n  let output: ReactNode;\n\n  if (shouldRunAsKeyless) {\n    output = (\n      <KeylessProvider\n        rest={propsWithEnvs}\n        generateNonce={generateNonce}\n        generateStatePromise={generateStatePromise}\n        runningWithClaimedKeys={runningWithClaimedKeys}\n      >\n        {children}\n      </KeylessProvider>\n    );\n  } else {\n    output = (\n      <ClientClerkProvider\n        {...propsWithEnvs}\n        nonce={await generateNonce()}\n        initialState={await generateStatePromise()}\n      >\n        {children}\n      </ClientClerkProvider>\n    );\n  }\n\n  if (dynamic) {\n    return (\n      // TODO: fix types so AuthObject is compatible with InitialState\n      <PromisifiedAuthProvider authPromise={generateStatePromise() as unknown as Promise<InitialState>}>\n        {output}\n      </PromisifiedAuthProvider>\n    );\n  }\n  return output;\n}\n"], "mappings": ";AACA,SAAS,eAAe;AAExB,OAAO,WAAW;AAElB,SAAS,+BAA+B;AACxC,SAAS,0BAA0B;AAEnC,SAAS,kCAAkC;AAC3C,SAAS,gBAAgB;AACzB,SAAS,2BAA2B;AACpC,SAAS,kBAAkB,uBAAuB;AAClD,SAAS,kBAAkB,gCAAgC;AAE3D,MAAM,uBAAuB,MAAM,MAAM,eAAeA,wBAAuB;AAC7E,QAAM,UAAU,MAAM,iBAAiB;AACvC,QAAM,OAAO,mBAAmB,OAAO;AAEvC,SAAO;AACT,CAAC;AAED,MAAM,kBAAkB,MAAM,MAAM,eAAeC,mBAAkB;AACnE,QAAM,cAAc,MAAM,QAAQ;AAClC,QAAM,QAAQ,YAAY,IAAI,SAAS;AACvC,SAAO,QACH;AAAA;AAAA,IAEA,yBAAyB,YAAY,IAAI,yBAAyB,KAAK,EAAE,KAAK;AAAA;AACpF,CAAC;AAED,eAAsB,cACpB,OACA;AACA,QAAM,EAAE,UAAU,SAAS,GAAG,KAAK,IAAI;AAEvC,iBAAe,uBAAuB;AACpC,QAAI,CAAC,SAAS;AACZ,aAAO,QAAQ,QAAQ,IAAI;AAAA,IAC7B;AACA,QAAI,UAAU;AAKZ,aAAO,QAAQ,QAAQ,MAAM,qBAAqB,CAAC;AAAA,IACrD;AACA,WAAO,qBAAqB;AAAA,EAC9B;AAEA,iBAAe,gBAAgB;AAC7B,QAAI,CAAC,SAAS;AACZ,aAAO,QAAQ,QAAQ,EAAE;AAAA,IAC3B;AACA,QAAI,UAAU;AAKZ,aAAO,QAAQ,QAAQ,MAAM,gBAAgB,CAAC;AAAA,IAChD;AACA,WAAO,gBAAgB;AAAA,EACzB;AAEA,QAAM,gBAAgB,2BAA2B;AAAA,IAC/C,GAAG;AAAA,EACL,CAAC;AAED,QAAM,EAAE,oBAAoB,uBAAuB,IAAI,MAAM,iBAAiB,aAAa;AAE3F,MAAI;AAEJ,MAAI,oBAAoB;AACtB,aACE;AAAA,MAAC;AAAA;AAAA,QACC,MAAM;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA;AAAA,MAEC;AAAA,IACH;AAAA,EAEJ,OAAO;AACL,aACE;AAAA,MAAC;AAAA;AAAA,QACE,GAAG;AAAA,QACJ,OAAO,MAAM,cAAc;AAAA,QAC3B,cAAc,MAAM,qBAAqB;AAAA;AAAA,MAExC;AAAA,IACH;AAAA,EAEJ;AAEA,MAAI,SAAS;AACX;AAAA;AAAA,MAEE,oCAAC,2BAAwB,aAAa,qBAAqB,KACxD,MACH;AAAA;AAAA,EAEJ;AACA,SAAO;AACT;", "names": ["getDynamicClerkState", "getNonceHeaders"]}