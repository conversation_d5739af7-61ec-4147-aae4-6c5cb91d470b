{"version": 3, "sources": ["../../../../src/app-router/server/controlComponents.tsx"], "sourcesContent": ["import type { ProtectProps } from '@clerk/clerk-react';\nimport React from 'react';\n\nimport { auth } from './auth';\n\nexport async function SignedIn(props: React.PropsWithChildren): Promise<React.JSX.Element | null> {\n  const { children } = props;\n  const { userId } = await auth();\n  return userId ? <>{children}</> : null;\n}\n\nexport async function SignedOut(props: React.PropsWithChildren): Promise<React.JSX.Element | null> {\n  const { children } = props;\n  const { userId } = await auth();\n  return userId ? null : <>{children}</>;\n}\n\n/**\n * Use `<Protect/>` in order to prevent unauthenticated or unauthorized users from accessing the children passed to the component.\n *\n * Examples:\n * ```\n * <Protect permission=\"a_permission_key\" />\n * <Protect role=\"a_role_key\" />\n * <Protect condition={(has) => has({permission:\"a_permission_key\"})} />\n * <Protect condition={(has) => has({role:\"a_role_key\"})} />\n * <Protect fallback={<p>Unauthorized</p>} />\n * ```\n */\nexport async function Protect(props: ProtectProps): Promise<React.JSX.Element | null> {\n  const { children, fallback, ...restAuthorizedParams } = props;\n  const { has, userId } = await auth();\n\n  /**\n   * Fallback to UI provided by user or `null` if authorization checks failed\n   */\n  const unauthorized = fallback ? <>{fallback}</> : null;\n\n  const authorized = <>{children}</>;\n\n  if (!userId) {\n    return unauthorized;\n  }\n\n  /**\n   * Check against the results of `has` called inside the callback\n   */\n  if (typeof restAuthorizedParams.condition === 'function') {\n    return restAuthorizedParams.condition(has) ? authorized : unauthorized;\n  }\n\n  if (\n    restAuthorizedParams.role ||\n    restAuthorizedParams.permission ||\n    restAuthorizedParams.feature ||\n    restAuthorizedParams.plan\n  ) {\n    return has(restAuthorizedParams) ? authorized : unauthorized;\n  }\n\n  /**\n   * If neither of the authorization params are passed behave as the `<SignedIn/>`.\n   * If fallback is present render that instead of rendering nothing.\n   */\n  return authorized;\n}\n"], "mappings": ";AACA,OAAO,WAAW;AAElB,SAAS,YAAY;AAErB,eAAsB,SAAS,OAAmE;AAChG,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,OAAO,IAAI,MAAM,KAAK;AAC9B,SAAO,SAAS,0DAAG,QAAS,IAAM;AACpC;AAEA,eAAsB,UAAU,OAAmE;AACjG,QAAM,EAAE,SAAS,IAAI;AACrB,QAAM,EAAE,OAAO,IAAI,MAAM,KAAK;AAC9B,SAAO,SAAS,OAAO,0DAAG,QAAS;AACrC;AAcA,eAAsB,QAAQ,OAAwD;AACpF,QAAM,EAAE,UAAU,UAAU,GAAG,qBAAqB,IAAI;AACxD,QAAM,EAAE,KAAK,OAAO,IAAI,MAAM,KAAK;AAKnC,QAAM,eAAe,WAAW,0DAAG,QAAS,IAAM;AAElD,QAAM,aAAa,0DAAG,QAAS;AAE/B,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AAKA,MAAI,OAAO,qBAAqB,cAAc,YAAY;AACxD,WAAO,qBAAqB,UAAU,GAAG,IAAI,aAAa;AAAA,EAC5D;AAEA,MACE,qBAAqB,QACrB,qBAAqB,cACrB,qBAAqB,WACrB,qBAAqB,MACrB;AACA,WAAO,IAAI,oBAAoB,IAAI,aAAa;AAAA,EAClD;AAMA,SAAO;AACT;", "names": []}