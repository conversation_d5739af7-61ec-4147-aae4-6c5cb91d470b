{"version": 3, "sources": ["../../../src/client-boundary/ClerkProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/compat/router';\nimport React from 'react';\n\nimport { ClientClerkProvider } from '../app-router/client/ClerkProvider';\nimport { ClerkProvider as PageClerkProvider } from '../pages/ClerkProvider';\nimport { type NextClerkProviderProps } from '../types';\n\n/**\n * This is a compatibility layer to support a single ClerkProvider component in both the app and pages routers.\n */\nexport function ClerkProvider(props: NextClerkProviderProps) {\n  const router = useRouter();\n\n  const Provider = router ? PageClerkProvider : ClientClerkProvider;\n\n  return <Provider {...props} />;\n}\n"], "mappings": ";;AAEA,SAAS,iBAAiB;AAC1B,OAAO,WAAW;AAElB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB,yBAAyB;AAM5C,SAAS,cAAc,OAA+B;AAC3D,QAAM,SAAS,UAAU;AAEzB,QAAM,WAAW,SAAS,oBAAoB;AAE9C,SAAO,oCAAC,YAAU,GAAG,OAAO;AAC9B;", "names": []}