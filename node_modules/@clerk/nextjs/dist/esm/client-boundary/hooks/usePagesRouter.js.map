{"version": 3, "sources": ["../../../../src/client-boundary/hooks/usePagesRouter.tsx"], "sourcesContent": ["import { useRouter } from 'next/compat/router';\n\nexport const usePagesRouter = () => {\n  // The compat version of useRouter returns null instead of throwing an error\n  // when used inside app router instead of pages router\n  // we use it to detect if the component is used inside pages or app router\n  // so we can use the correct algorithm to get the path\n  return { pagesRouter: useRouter() };\n};\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAEnB,MAAM,iBAAiB,MAAM;AAKlC,SAAO,EAAE,aAAa,UAAU,EAAE;AACpC;", "names": []}