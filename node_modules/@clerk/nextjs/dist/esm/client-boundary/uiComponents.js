"use client";
import "../chunk-BUSYA2B4.js";
import {
  OrganizationProfile as BaseOrganizationProfile,
  SignIn as BaseSignIn,
  SignUp as BaseSignUp,
  UserProfile as BaseUserProfile
} from "@clerk/clerk-react";
import React from "react";
import { useEnforceCorrectRoutingProps } from "./hooks/useEnforceRoutingProps";
import {
  CreateOrganization,
  OrganizationList,
  OrganizationSwitcher,
  SignInButton,
  SignInWithMetamaskButton,
  SignOutButton,
  SignUpButton,
  UserButton,
  GoogleOneTap,
  Waitlist,
  PricingTable
} from "@clerk/clerk-react";
const UserProfile = Object.assign(
  (props) => {
    return /* @__PURE__ */ React.createElement(BaseUserProfile, { ...useEnforceCorrectRoutingProps("UserProfile", props) });
  },
  { ...BaseUserProfile }
);
const OrganizationProfile = Object.assign(
  (props) => {
    return /* @__PURE__ */ React.createElement(BaseOrganizationProfile, { ...useEnforceCorrectRoutingProps("OrganizationProfile", props) });
  },
  { ...BaseOrganizationProfile }
);
const SignIn = (props) => {
  return /* @__PURE__ */ React.createElement(BaseSignIn, { ...useEnforceCorrectRoutingProps("SignIn", props, false) });
};
const SignUp = (props) => {
  return /* @__PURE__ */ React.createElement(BaseSignUp, { ...useEnforceCorrectRoutingProps("SignUp", props, false) });
};
export {
  CreateOrganization,
  GoogleOneTap,
  OrganizationList,
  OrganizationProfile,
  OrganizationSwitcher,
  PricingTable,
  SignIn,
  SignInButton,
  SignInWithMetamaskButton,
  SignOutButton,
  SignUp,
  SignUpButton,
  UserButton,
  UserProfile,
  Waitlist
};
//# sourceMappingURL=uiComponents.js.map