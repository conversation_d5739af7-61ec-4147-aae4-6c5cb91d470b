import "./chunk-BUSYA2B4.js";
import {
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isReverificationCancelledError,
  isMetamaskError,
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus
} from "./client-boundary/hooks";
import { isClerkAPIResponseError } from "@clerk/clerk-react/errors";
export {
  EmailLinkErrorCode,
  EmailLinkErrorCodeStatus,
  isClerkAPIResponseError,
  isClerkRuntimeError,
  isEmailLinkError,
  isKnownError,
  isMetamaskError,
  isReverificationCancelledError
};
//# sourceMappingURL=errors.js.map