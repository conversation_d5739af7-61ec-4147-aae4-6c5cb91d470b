{"version": 3, "sources": ["../../../../src/runtime/browser/safe-node-apis.js"], "sourcesContent": ["/**\n * This file is used for conditional imports to mitigate bundling issues with Next.js server actions on version prior to 14.1.0.\n */\nconst fs = undefined;\nconst path = undefined;\nconst cwd = undefined;\n\nmodule.exports = { fs, path, cwd };\n"], "mappings": ";;;AAAA;AAAA;AAGA,UAAM,KAAK;AACX,UAAM,OAAO;AACb,UAAM,MAAM;AAEZ,WAAO,UAAU,EAAE,IAAI,MAAM,IAAI;AAAA;AAAA;", "names": []}