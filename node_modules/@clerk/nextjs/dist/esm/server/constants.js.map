{"version": 3, "sources": ["../../../src/server/constants.ts"], "sourcesContent": ["import { apiUrlFromPublishableKey } from '@clerk/shared/apiUrlFromPublishableKey';\nimport { isTruthy } from '@clerk/shared/underscore';\n\nexport const CLERK_JS_VERSION = process.env.NEXT_PUBLIC_CLERK_JS_VERSION || '';\nexport const CLERK_JS_URL = process.env.NEXT_PUBLIC_CLERK_JS_URL || '';\nexport const API_VERSION = process.env.CLERK_API_VERSION || 'v1';\nexport const SECRET_KEY = process.env.CLERK_SECRET_KEY || '';\nexport const PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY || '';\nexport const ENCRYPTION_KEY = process.env.CLERK_ENCRYPTION_KEY || '';\nexport const API_URL = process.env.CLERK_API_URL || apiUrlFromPublishableKey(PUBLISHABLE_KEY);\nexport const DOMAIN = process.env.NEXT_PUBLIC_CLERK_DOMAIN || '';\nexport const PROXY_URL = process.env.NEXT_PUBLIC_CLERK_PROXY_URL || '';\nexport const IS_SATELLITE = isTruthy(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE) || false;\nexport const SIGN_IN_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_IN_URL || '';\nexport const SIGN_UP_URL = process.env.NEXT_PUBLIC_CLERK_SIGN_UP_URL || '';\nexport const SDK_METADATA = {\n  name: PACKAGE_NAME,\n  version: PACKAGE_VERSION,\n  environment: process.env.NODE_ENV,\n};\n\nexport const TELEMETRY_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED);\nexport const TELEMETRY_DEBUG = isTruthy(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);\n\nexport const KEYLESS_DISABLED = isTruthy(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED) || false;\n"], "mappings": ";AAAA,SAAS,gCAAgC;AACzC,SAAS,gBAAgB;AAElB,MAAM,mBAAmB,QAAQ,IAAI,gCAAgC;AACrE,MAAM,eAAe,QAAQ,IAAI,4BAA4B;AAC7D,MAAM,cAAc,QAAQ,IAAI,qBAAqB;AACrD,MAAM,aAAa,QAAQ,IAAI,oBAAoB;AACnD,MAAM,kBAAkB,QAAQ,IAAI,qCAAqC;AACzE,MAAM,iBAAiB,QAAQ,IAAI,wBAAwB;AAC3D,MAAM,UAAU,QAAQ,IAAI,iBAAiB,yBAAyB,eAAe;AACrF,MAAM,SAAS,QAAQ,IAAI,4BAA4B;AACvD,MAAM,YAAY,QAAQ,IAAI,+BAA+B;AAC7D,MAAM,eAAe,SAAS,QAAQ,IAAI,8BAA8B,KAAK;AAC7E,MAAM,cAAc,QAAQ,IAAI,iCAAiC;AACjE,MAAM,cAAc,QAAQ,IAAI,iCAAiC;AACjE,MAAM,eAAe;AAAA,EAC1B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,aAAa,QAAQ,IAAI;AAC3B;AAEO,MAAM,qBAAqB,SAAS,QAAQ,IAAI,oCAAoC;AACpF,MAAM,kBAAkB,SAAS,QAAQ,IAAI,iCAAiC;AAE9E,MAAM,mBAAmB,SAAS,QAAQ,IAAI,kCAAkC,KAAK;", "names": []}