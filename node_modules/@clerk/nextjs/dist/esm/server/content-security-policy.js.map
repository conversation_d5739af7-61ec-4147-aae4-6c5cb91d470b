{"version": 3, "sources": ["../../../src/server/content-security-policy.ts"], "sourcesContent": ["import { constants } from '@clerk/backend/internal';\n\n/**\n * Valid CSP directives according to the CSP Level 3 specification\n */\nexport type ContentSecurityPolicyDirective =\n  // Default resource directives\n  | 'connect-src'\n  | 'default-src'\n  | 'font-src'\n  | 'img-src'\n  | 'media-src'\n  | 'object-src'\n  | 'script-src'\n  | 'style-src'\n  // Framing and navigation directives\n  | 'base-uri'\n  | 'child-src'\n  | 'form-action'\n  | 'frame-ancestors'\n  | 'frame-src'\n  | 'manifest-src'\n  | 'navigate-to'\n  | 'prefetch-src'\n  | 'worker-src'\n  // Sandbox and plugin directives\n  | 'plugin-types'\n  | 'require-sri-for'\n  | 'sandbox'\n  // Trusted types and upgrade directives\n  | 'block-all-mixed-content'\n  | 'require-trusted-types-for'\n  | 'trusted-types'\n  | 'upgrade-insecure-requests'\n  // Reporting directives\n  | 'report-to'\n  | 'report-uri'\n  // CSP Level 3 additional directives\n  | 'script-src-attr'\n  | 'script-src-elem'\n  | 'style-src-attr'\n  | 'style-src-elem';\n\n/**\n * Partial record of directives and their values\n */\ntype ContentSecurityPolicyValues = Partial<Record<ContentSecurityPolicyDirective, string[]>>;\n\n/**\n * Directives and their values\n */\ntype ContentSecurityPolicyDirectiveSet = Record<ContentSecurityPolicyDirective, Set<string>>;\n\nexport interface ContentSecurityPolicyHeaders {\n  /**\n   * Array of formatted headers to be added to the response.\n   *\n   * Includes both standard and report-only headers when applicable.\n   * Includes nonce when strict mode is enabled.\n   */\n  headers: [string, string][];\n}\n\nexport interface ContentSecurityPolicyOptions {\n  /**\n   * When set to true, enhances security by applying the `strict-dynamic` attribute to the `script-src` CSP directive\n   */\n  strict?: boolean;\n  /**\n   * Custom CSP directives to merge with Clerk's default directives\n   */\n  directives?: Partial<Record<ContentSecurityPolicyDirective, string[]>>;\n  /**\n   * When set to true, the Content-Security-Policy-Report-Only header will be used instead of\n   * Content-Security-Policy. This allows monitoring policy violations without blocking content.\n   */\n  reportOnly?: boolean;\n  /**\n   * Specifies a reporting endpoint for CSP violations. This value will be used in the\n   * 'report-to' directive of the Content-Security-Policy header.\n   */\n  reportTo?: string;\n}\n\nclass ContentSecurityPolicyDirectiveManager {\n  /** Set of special keywords that require quoting in CSP directives */\n  private static readonly KEYWORDS = new Set([\n    'none',\n    'self',\n    'strict-dynamic',\n    'unsafe-eval',\n    'unsafe-hashes',\n    'unsafe-inline',\n  ]);\n\n  /** Default CSP directives and their values */\n  static readonly DEFAULT_DIRECTIVES: ContentSecurityPolicyValues = {\n    'connect-src': [\n      'self',\n      'https://clerk-telemetry.com',\n      'https://*.clerk-telemetry.com',\n      'https://api.stripe.com',\n      'https://maps.googleapis.com',\n    ],\n    'default-src': ['self'],\n    'form-action': ['self'],\n    'frame-src': [\n      'self',\n      'https://challenges.cloudflare.com',\n      'https://*.js.stripe.com',\n      'https://js.stripe.com',\n      'https://hooks.stripe.com',\n    ],\n    'img-src': ['self', 'https://img.clerk.com'],\n    'script-src': [\n      'self',\n      ...(process.env.NODE_ENV !== 'production' ? ['unsafe-eval'] : []),\n      'unsafe-inline',\n      'https:',\n      'http:',\n      'https://*.js.stripe.com',\n      'https://js.stripe.com',\n      'https://maps.googleapis.com',\n    ],\n    'style-src': ['self', 'unsafe-inline'],\n    'worker-src': ['self', 'blob:'],\n  };\n\n  /**\n   * Creates a new ContentSecurityPolicyDirectiveSet with default values\n   * @returns A new ContentSecurityPolicyDirectiveSet with default values\n   */\n  static createDefaultDirectives(): ContentSecurityPolicyDirectiveSet {\n    return Object.entries(this.DEFAULT_DIRECTIVES).reduce((acc, [key, values]) => {\n      acc[key as ContentSecurityPolicyDirective] = new Set(values);\n      return acc;\n    }, {} as ContentSecurityPolicyDirectiveSet);\n  }\n\n  /**\n   * Checks if a value is a special keyword that requires quoting\n   * @param value - The value to check\n   * @returns True if the value is a special keyword\n   */\n  static isKeyword(value: string): boolean {\n    return this.KEYWORDS.has(value.replace(/^'|'$/g, ''));\n  }\n\n  /**\n   * Formats a value according to CSP rules, adding quotes for special keywords\n   * @param value - The value to format\n   * @returns The formatted value\n   */\n  static formatValue(value: string): string {\n    const unquoted = value.replace(/^'|'$/g, '');\n    return this.isKeyword(unquoted) ? `'${unquoted}'` : value;\n  }\n\n  /**\n   * Handles directive values, ensuring proper formatting and special case handling\n   * @param values - Array of values to process\n   * @returns Set of formatted values\n   */\n  static handleDirectiveValues(values: string[]): Set<string> {\n    const result = new Set<string>();\n\n    if (values.includes(\"'none'\") || values.includes('none')) {\n      result.add(\"'none'\");\n      return result;\n    }\n\n    values.forEach(v => result.add(this.formatValue(v)));\n    return result;\n  }\n}\n\n/**\n * Handles merging of existing directives with new values\n * @param mergedCSP - The current merged CSP state\n * @param key - The directive key to handle\n * @param values - New values to merge\n */\nfunction handleExistingDirective(\n  mergedCSP: ContentSecurityPolicyDirectiveSet,\n  key: ContentSecurityPolicyDirective,\n  values: string[],\n) {\n  // None overrides all other values\n  if (values.includes(\"'none'\") || values.includes('none')) {\n    mergedCSP[key] = new Set([\"'none'\"]);\n    return;\n  }\n\n  // For existing directives, merge the values rather than replacing\n  const deduplicatedSet = new Set<string>();\n\n  mergedCSP[key].forEach(value => {\n    deduplicatedSet.add(ContentSecurityPolicyDirectiveManager.formatValue(value));\n  });\n\n  values.forEach(value => {\n    deduplicatedSet.add(ContentSecurityPolicyDirectiveManager.formatValue(value));\n  });\n\n  mergedCSP[key] = deduplicatedSet;\n}\n\n/**\n * Handles custom directives that are not part of the default set\n * @param customDirectives - Map of custom directives\n * @param key - The directive key\n * @param values - Values for the directive\n */\nfunction handleCustomDirective(customDirectives: Map<string, Set<string>>, key: string, values: string[]) {\n  // None overrides all other values\n  if (values.includes(\"'none'\") || values.includes('none')) {\n    customDirectives.set(key, new Set([\"'none'\"]));\n    return;\n  }\n\n  const formattedValues = new Set<string>();\n  values.forEach(value => {\n    const formattedValue = ContentSecurityPolicyDirectiveManager.formatValue(value);\n    formattedValues.add(formattedValue);\n  });\n\n  customDirectives.set(key, formattedValues);\n}\n\n/**\n * Applies formatting to the CSP header\n * @param mergedCSP - The merged CSP state to format\n * @returns Formatted CSP header string\n */\nfunction formatCSPHeader(mergedCSP: Record<string, Set<string>>): string {\n  return Object.entries(mergedCSP)\n    .sort(([a], [b]) => a.localeCompare(b))\n    .map(([key, values]) => {\n      const valueObjs = Array.from(values).map(v => ({\n        raw: v,\n        formatted: ContentSecurityPolicyDirectiveManager.formatValue(v),\n      }));\n\n      return `${key} ${valueObjs.map(item => item.formatted).join(' ')}`;\n    })\n    .join('; ');\n}\n\n/**\n * Generates a secure random nonce for CSP headers\n * @returns A base64-encoded random nonce\n */\nexport function generateNonce(): string {\n  const randomBytes = new Uint8Array(16);\n  crypto.getRandomValues(randomBytes);\n  const binaryString = Array.from(randomBytes, byte => String.fromCharCode(byte)).join('');\n  return btoa(binaryString);\n}\n\n/**\n * Builds a complete set of CSP directives by combining defaults with custom directives,\n * applying special configurations like strict mode and nonce, and formatting them into a valid CSP string.\n */\nfunction buildContentSecurityPolicyDirectives(\n  strict: boolean,\n  host: string,\n  customDirectives?: Partial<Record<ContentSecurityPolicyDirective, string[]>>,\n  nonce?: string,\n): string {\n  const directives = Object.entries(ContentSecurityPolicyDirectiveManager.DEFAULT_DIRECTIVES).reduce(\n    (acc, [key, values]) => {\n      acc[key as ContentSecurityPolicyDirective] = new Set(values);\n      return acc;\n    },\n    {} as ContentSecurityPolicyDirectiveSet,\n  );\n\n  directives['connect-src'].add(host);\n\n  if (strict) {\n    directives['script-src'].delete('http:');\n    directives['script-src'].delete('https:');\n    directives['script-src'].add(\"'strict-dynamic'\");\n    if (nonce) {\n      directives['script-src'].add(`'nonce-${nonce}'`);\n    }\n  }\n\n  if (customDirectives) {\n    const customDirectivesMap = new Map<string, Set<string>>();\n    Object.entries(customDirectives).forEach(([key, values]) => {\n      const valuesArray = Array.isArray(values) ? values : [values];\n      if (ContentSecurityPolicyDirectiveManager.DEFAULT_DIRECTIVES[key as ContentSecurityPolicyDirective]) {\n        handleExistingDirective(directives, key as ContentSecurityPolicyDirective, valuesArray);\n      } else {\n        handleCustomDirective(customDirectivesMap, key, valuesArray);\n      }\n    });\n\n    // Merge all custom directives into the final directives object\n    customDirectivesMap.forEach((values, key) => {\n      directives[key as ContentSecurityPolicyDirective] = values;\n    });\n  }\n\n  return formatCSPHeader(directives);\n}\n\n/**\n * Creates Content Security Policy (CSP) headers with the specified configuration\n * @returns Object containing the formatted CSP headers\n */\nexport function createContentSecurityPolicyHeaders(\n  host: string,\n  options: ContentSecurityPolicyOptions,\n): ContentSecurityPolicyHeaders {\n  const headers: [string, string][] = [];\n\n  const nonce = options.strict ? generateNonce() : undefined;\n\n  let cspHeader = buildContentSecurityPolicyDirectives(options.strict ?? false, host, options.directives, nonce);\n\n  if (options.reportTo) {\n    cspHeader += '; report-to csp-endpoint';\n    headers.push([constants.Headers.ReportingEndpoints, `csp-endpoint=\"${options.reportTo}\"`]);\n  }\n\n  if (options.reportOnly) {\n    headers.push([constants.Headers.ContentSecurityPolicyReportOnly, cspHeader]);\n  } else {\n    headers.push([constants.Headers.ContentSecurityPolicy, cspHeader]);\n  }\n\n  if (nonce) {\n    headers.push([constants.Headers.Nonce, nonce]);\n  }\n\n  return {\n    headers,\n  };\n}\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAoF1B,MAAM,sCAAsC;AAAA;AAAA;AAAA;AAAA;AAAA,EAgD1C,OAAO,0BAA6D;AAClE,WAAO,OAAO,QAAQ,KAAK,kBAAkB,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,MAAM,MAAM;AAC5E,UAAI,GAAqC,IAAI,IAAI,IAAI,MAAM;AAC3D,aAAO;AAAA,IACT,GAAG,CAAC,CAAsC;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,UAAU,OAAwB;AACvC,WAAO,KAAK,SAAS,IAAI,MAAM,QAAQ,UAAU,EAAE,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,YAAY,OAAuB;AACxC,UAAM,WAAW,MAAM,QAAQ,UAAU,EAAE;AAC3C,WAAO,KAAK,UAAU,QAAQ,IAAI,IAAI,QAAQ,MAAM;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,sBAAsB,QAA+B;AAC1D,UAAM,SAAS,oBAAI,IAAY;AAE/B,QAAI,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,MAAM,GAAG;AACxD,aAAO,IAAI,QAAQ;AACnB,aAAO;AAAA,IACT;AAEA,WAAO,QAAQ,OAAK,OAAO,IAAI,KAAK,YAAY,CAAC,CAAC,CAAC;AACnD,WAAO;AAAA,EACT;AACF;AAAA;AA1FM,sCAEoB,WAAW,oBAAI,IAAI;AAAA,EACzC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAAA;AATG,sCAYY,qBAAkD;AAAA,EAChE,eAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,eAAe,CAAC,MAAM;AAAA,EACtB,eAAe,CAAC,MAAM;AAAA,EACtB,aAAa;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,WAAW,CAAC,QAAQ,uBAAuB;AAAA,EAC3C,cAAc;AAAA,IACZ;AAAA,IACA,GAAI,QAAQ,IAAI,aAAa,eAAe,CAAC,aAAa,IAAI,CAAC;AAAA,IAC/D;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAAA,EACA,aAAa,CAAC,QAAQ,eAAe;AAAA,EACrC,cAAc,CAAC,QAAQ,OAAO;AAChC;AAwDF,SAAS,wBACP,WACA,KACA,QACA;AAEA,MAAI,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,MAAM,GAAG;AACxD,cAAU,GAAG,IAAI,oBAAI,IAAI,CAAC,QAAQ,CAAC;AACnC;AAAA,EACF;AAGA,QAAM,kBAAkB,oBAAI,IAAY;AAExC,YAAU,GAAG,EAAE,QAAQ,WAAS;AAC9B,oBAAgB,IAAI,sCAAsC,YAAY,KAAK,CAAC;AAAA,EAC9E,CAAC;AAED,SAAO,QAAQ,WAAS;AACtB,oBAAgB,IAAI,sCAAsC,YAAY,KAAK,CAAC;AAAA,EAC9E,CAAC;AAED,YAAU,GAAG,IAAI;AACnB;AAQA,SAAS,sBAAsB,kBAA4C,KAAa,QAAkB;AAExG,MAAI,OAAO,SAAS,QAAQ,KAAK,OAAO,SAAS,MAAM,GAAG;AACxD,qBAAiB,IAAI,KAAK,oBAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC7C;AAAA,EACF;AAEA,QAAM,kBAAkB,oBAAI,IAAY;AACxC,SAAO,QAAQ,WAAS;AACtB,UAAM,iBAAiB,sCAAsC,YAAY,KAAK;AAC9E,oBAAgB,IAAI,cAAc;AAAA,EACpC,CAAC;AAED,mBAAiB,IAAI,KAAK,eAAe;AAC3C;AAOA,SAAS,gBAAgB,WAAgD;AACvE,SAAO,OAAO,QAAQ,SAAS,EAC5B,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,EACrC,IAAI,CAAC,CAAC,KAAK,MAAM,MAAM;AACtB,UAAM,YAAY,MAAM,KAAK,MAAM,EAAE,IAAI,QAAM;AAAA,MAC7C,KAAK;AAAA,MACL,WAAW,sCAAsC,YAAY,CAAC;AAAA,IAChE,EAAE;AAEF,WAAO,GAAG,GAAG,IAAI,UAAU,IAAI,UAAQ,KAAK,SAAS,EAAE,KAAK,GAAG,CAAC;AAAA,EAClE,CAAC,EACA,KAAK,IAAI;AACd;AAMO,SAAS,gBAAwB;AACtC,QAAM,cAAc,IAAI,WAAW,EAAE;AACrC,SAAO,gBAAgB,WAAW;AAClC,QAAM,eAAe,MAAM,KAAK,aAAa,UAAQ,OAAO,aAAa,IAAI,CAAC,EAAE,KAAK,EAAE;AACvF,SAAO,KAAK,YAAY;AAC1B;AAMA,SAAS,qCACP,QACA,MACA,kBACA,OACQ;AACR,QAAM,aAAa,OAAO,QAAQ,sCAAsC,kBAAkB,EAAE;AAAA,IAC1F,CAAC,KAAK,CAAC,KAAK,MAAM,MAAM;AACtB,UAAI,GAAqC,IAAI,IAAI,IAAI,MAAM;AAC3D,aAAO;AAAA,IACT;AAAA,IACA,CAAC;AAAA,EACH;AAEA,aAAW,aAAa,EAAE,IAAI,IAAI;AAElC,MAAI,QAAQ;AACV,eAAW,YAAY,EAAE,OAAO,OAAO;AACvC,eAAW,YAAY,EAAE,OAAO,QAAQ;AACxC,eAAW,YAAY,EAAE,IAAI,kBAAkB;AAC/C,QAAI,OAAO;AACT,iBAAW,YAAY,EAAE,IAAI,UAAU,KAAK,GAAG;AAAA,IACjD;AAAA,EACF;AAEA,MAAI,kBAAkB;AACpB,UAAM,sBAAsB,oBAAI,IAAyB;AACzD,WAAO,QAAQ,gBAAgB,EAAE,QAAQ,CAAC,CAAC,KAAK,MAAM,MAAM;AAC1D,YAAM,cAAc,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAC5D,UAAI,sCAAsC,mBAAmB,GAAqC,GAAG;AACnG,gCAAwB,YAAY,KAAuC,WAAW;AAAA,MACxF,OAAO;AACL,8BAAsB,qBAAqB,KAAK,WAAW;AAAA,MAC7D;AAAA,IACF,CAAC;AAGD,wBAAoB,QAAQ,CAAC,QAAQ,QAAQ;AAC3C,iBAAW,GAAqC,IAAI;AAAA,IACtD,CAAC;AAAA,EACH;AAEA,SAAO,gBAAgB,UAAU;AACnC;AAMO,SAAS,mCACd,MACA,SAC8B;AA3ThC;AA4TE,QAAM,UAA8B,CAAC;AAErC,QAAM,QAAQ,QAAQ,SAAS,cAAc,IAAI;AAEjD,MAAI,YAAY,sCAAqC,aAAQ,WAAR,YAAkB,OAAO,MAAM,QAAQ,YAAY,KAAK;AAE7G,MAAI,QAAQ,UAAU;AACpB,iBAAa;AACb,YAAQ,KAAK,CAAC,UAAU,QAAQ,oBAAoB,iBAAiB,QAAQ,QAAQ,GAAG,CAAC;AAAA,EAC3F;AAEA,MAAI,QAAQ,YAAY;AACtB,YAAQ,KAAK,CAAC,UAAU,QAAQ,iCAAiC,SAAS,CAAC;AAAA,EAC7E,OAAO;AACL,YAAQ,KAAK,CAAC,UAAU,QAAQ,uBAAuB,SAAS,CAAC;AAAA,EACnE;AAEA,MAAI,OAAO;AACT,YAAQ,KAAK,CAAC,UAAU,QAAQ,OAAO,KAAK,CAAC;AAAA,EAC/C;AAEA,SAAO;AAAA,IACL;AAAA,EACF;AACF;", "names": []}