import "../chunk-BUSYA2B4.js";
import { constants } from "@clerk/backend/internal";
import { isTruthy } from "@clerk/shared/underscore";
import { withLogger } from "../utils/debugLogger";
import { isNextWithUnstableServerActions } from "../utils/sdk-versions";
import { getAuthDataFromRequest } from "./data/getAuthDataFromRequest";
import { getAuthAuthHeaderMissing } from "./errors";
import { detectClerkMiddleware, getHeader } from "./headers-utils";
import { assertAuthStatus } from "./utils";
const createAsyncGetAuth = ({
  debugLoggerName,
  noAuthStatusMessage
}) => withLogger(debugLoggerName, (logger) => {
  return async (req, opts) => {
    if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {
      logger.enable();
    }
    if (!detectClerkMiddleware(req)) {
      if (isNextWithUnstableServerActions) {
        assertAuthStatus(req, noAuthStatusMessage);
      }
      const missConfiguredMiddlewareLocation = await import("./fs/middleware-location.js").then((m) => m.suggestMiddlewareLocation()).catch(() => void 0);
      if (missConfiguredMiddlewareLocation) {
        throw new Error(missConfiguredMiddlewareLocation);
      }
      assertAuthStatus(req, noAuthStatusMessage);
    }
    return getAuthDataFromRequest(req, { ...opts, logger });
  };
});
const createSyncGetAuth = ({
  debugLoggerName,
  noAuthStatusMessage
}) => withLogger(debugLoggerName, (logger) => {
  return (req, opts) => {
    if (isTruthy(getHeader(req, constants.Headers.EnableDebug))) {
      logger.enable();
    }
    assertAuthStatus(req, noAuthStatusMessage);
    return getAuthDataFromRequest(req, { ...opts, logger });
  };
});
const getAuth = createSyncGetAuth({
  debugLoggerName: "getAuth()",
  noAuthStatusMessage: getAuthAuthHeaderMissing()
});
export {
  createAsyncGetAuth,
  createSyncGetAuth,
  getAuth
};
//# sourceMappingURL=createGetAuth.js.map