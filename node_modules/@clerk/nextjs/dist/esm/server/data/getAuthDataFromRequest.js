import "../../chunk-BUSYA2B4.js";
import { AuthStatus, constants, signedInAuthObject, signedOutAuthObject } from "@clerk/backend/internal";
import { decodeJwt } from "@clerk/backend/jwt";
import { API_URL, API_VERSION, PUBLISHABLE_KEY, SECRET_KEY } from "../constants";
import { getAuthKeyFromRequest, getHeader } from "../headers-utils";
import { assertTokenSignature, decryptClerkRequestData } from "../utils";
function getAuthDataFromRequest(req, opts = {}) {
  var _a, _b, _c;
  const authStatus = getAuthKeyFromRequest(req, "AuthStatus");
  const authToken = getAuthKeyFromRequest(req, "AuthToken");
  const authMessage = getAuthKeyFromRequest(req, "AuthMessage");
  const authReason = getAuthKeyFromRequest(req, "AuthReason");
  const authSignature = getAuthKeyFromRequest(req, "AuthSignature");
  (_a = opts.logger) == null ? void 0 : _a.debug("headers", { authStatus, authMessage, authReason });
  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);
  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);
  const options = {
    secretKey: (opts == null ? void 0 : opts.secretKey) || decryptedRequestData.secretKey || SECRET_KEY,
    publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,
    apiUrl: API_URL,
    apiVersion: API_VERSION,
    authStatus,
    authMessage,
    authReason
  };
  (_b = opts.logger) == null ? void 0 : _b.debug("auth options", options);
  let authObject;
  if (!authStatus || authStatus !== AuthStatus.SignedIn) {
    authObject = signedOutAuthObject(options);
  } else {
    assertTokenSignature(authToken, options.secretKey, authSignature);
    const jwt = decodeJwt(authToken);
    (_c = opts.logger) == null ? void 0 : _c.debug("jwt", jwt.raw);
    authObject = signedInAuthObject(options, jwt.raw.text, jwt.payload);
  }
  return authObject;
}
export {
  getAuthDataFromRequest
};
//# sourceMappingURL=getAuthDataFromRequest.js.map