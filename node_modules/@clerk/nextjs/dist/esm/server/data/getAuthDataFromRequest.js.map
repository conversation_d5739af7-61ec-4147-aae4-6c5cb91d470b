{"version": 3, "sources": ["../../../../src/server/data/getAuthDataFromRequest.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport { AuthStatus, constants, signedInAuthObject, signedOutAuthObject } from '@clerk/backend/internal';\nimport { decodeJwt } from '@clerk/backend/jwt';\n\nimport type { LoggerNoCommit } from '../../utils/debugLogger';\nimport { API_URL, API_VERSION, PUBLISHABLE_KEY, SECRET_KEY } from '../constants';\nimport { getAuthKeyFromRequest, getHeader } from '../headers-utils';\nimport type { RequestLike } from '../types';\nimport { assertTokenSignature, decryptClerkRequestData } from '../utils';\n\n/**\n * Given a request object, builds an auth object from the request data. Used in server-side environments to get access\n * to auth data for a given request.\n */\nexport function getAuthDataFromRequest(\n  req: RequestLike,\n  opts: { secretKey?: string; logger?: LoggerNoCommit } = {},\n): AuthObject {\n  const authStatus = getAuthKeyFromRequest(req, 'AuthStatus');\n  const authToken = getAuthKeyFromRequest(req, 'AuthToken');\n  const authMessage = getAuthKeyFromRequest(req, 'AuthMessage');\n  const authReason = getAuthKeyFromRequest(req, 'AuthReason');\n  const authSignature = getAuthKeyFromRequest(req, 'AuthSignature');\n\n  opts.logger?.debug('headers', { authStatus, authMessage, authReason });\n\n  const encryptedRequestData = getHeader(req, constants.Headers.ClerkRequestData);\n  const decryptedRequestData = decryptClerkRequestData(encryptedRequestData);\n\n  const options = {\n    secretKey: opts?.secretKey || decryptedRequestData.secretKey || SECRET_KEY,\n    publishableKey: decryptedRequestData.publishableKey || PUBLISHABLE_KEY,\n    apiUrl: API_URL,\n    apiVersion: API_VERSION,\n    authStatus,\n    authMessage,\n    authReason,\n  };\n\n  opts.logger?.debug('auth options', options);\n\n  let authObject;\n  if (!authStatus || authStatus !== AuthStatus.SignedIn) {\n    authObject = signedOutAuthObject(options);\n  } else {\n    assertTokenSignature(authToken as string, options.secretKey, authSignature);\n\n    const jwt = decodeJwt(authToken as string);\n\n    opts.logger?.debug('jwt', jwt.raw);\n\n    // @ts-expect-error -- Restrict parameter type of options to only list what's needed\n    authObject = signedInAuthObject(options, jwt.raw.text, jwt.payload);\n  }\n\n  return authObject;\n}\n"], "mappings": ";AACA,SAAS,YAAY,WAAW,oBAAoB,2BAA2B;AAC/E,SAAS,iBAAiB;AAG1B,SAAS,SAAS,aAAa,iBAAiB,kBAAkB;AAClE,SAAS,uBAAuB,iBAAiB;AAEjD,SAAS,sBAAsB,+BAA+B;AAMvD,SAAS,uBACd,KACA,OAAwD,CAAC,GAC7C;AAjBd;AAkBE,QAAM,aAAa,sBAAsB,KAAK,YAAY;AAC1D,QAAM,YAAY,sBAAsB,KAAK,WAAW;AACxD,QAAM,cAAc,sBAAsB,KAAK,aAAa;AAC5D,QAAM,aAAa,sBAAsB,KAAK,YAAY;AAC1D,QAAM,gBAAgB,sBAAsB,KAAK,eAAe;AAEhE,aAAK,WAAL,mBAAa,MAAM,WAAW,EAAE,YAAY,aAAa,WAAW;AAEpE,QAAM,uBAAuB,UAAU,KAAK,UAAU,QAAQ,gBAAgB;AAC9E,QAAM,uBAAuB,wBAAwB,oBAAoB;AAEzE,QAAM,UAAU;AAAA,IACd,YAAW,6BAAM,cAAa,qBAAqB,aAAa;AAAA,IAChE,gBAAgB,qBAAqB,kBAAkB;AAAA,IACvD,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF;AAEA,aAAK,WAAL,mBAAa,MAAM,gBAAgB;AAEnC,MAAI;AACJ,MAAI,CAAC,cAAc,eAAe,WAAW,UAAU;AACrD,iBAAa,oBAAoB,OAAO;AAAA,EAC1C,OAAO;AACL,yBAAqB,WAAqB,QAAQ,WAAW,aAAa;AAE1E,UAAM,MAAM,UAAU,SAAmB;AAEzC,eAAK,WAAL,mBAAa,MAAM,OAAO,IAAI;AAG9B,iBAAa,mBAAmB,SAAS,IAAI,IAAI,MAAM,IAAI,OAAO;AAAA,EACpE;AAEA,SAAO;AACT;", "names": []}