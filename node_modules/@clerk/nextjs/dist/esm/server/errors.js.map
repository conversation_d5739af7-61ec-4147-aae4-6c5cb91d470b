{"version": 3, "sources": ["../../../src/server/errors.ts"], "sourcesContent": ["export const missingDomainAndProxy = `\nMissing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.\n\n1) With middleware\n   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'\n   `;\n\nexport const missingSignInUrlInDev = `\nInvalid signInUrl. A satellite application requires a signInUrl for development instances.\nCheck if signInUrl is missing from your configuration or if it is not an absolute URL\n\n1) With middleware\n   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});\n2) With environment variables e.g.\n   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'\n   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`;\n\nexport const getAuthAuthHeaderMissing = () => authAuthHeaderMissing('getAuth');\n\nexport const authAuthHeaderMissing = (helperName = 'auth', prefixSteps?: string[]) =>\n  `Clerk: ${helperName}() was called but Clerk can't detect usage of clerkMiddleware(). Please ensure the following:\n- ${prefixSteps ? [...prefixSteps, ''].join('\\n- ') : ' '}clerkMiddleware() is used in your Next.js Middleware.\n- Your Middleware matcher is configured to match this route or page.\n- If you are using the src directory, make sure the Middleware file is inside of it.\n\nFor more details, see https://clerk.com/docs/quickstarts/nextjs\n`;\n\nexport const authSignatureInvalid = `Clerk: Unable to verify request, this usually means the Clerk middleware did not run. Ensure Clerk's middleware is properly integrated and matches the current route. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware. (code=auth_signature_invalid)`;\n\nexport const encryptionKeyInvalid = `Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\n\nexport const encryptionKeyInvalidDev = `Clerk: Unable to decrypt request data.\\n\\nRefresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.\\n\\nFor more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`;\n"], "mappings": ";AAAO,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAU9B,MAAM,wBAAwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAU9B,MAAM,2BAA2B,MAAM,sBAAsB,SAAS;AAEtE,MAAM,wBAAwB,CAAC,aAAa,QAAQ,gBACzD,UAAU,UAAU;AAAA,IAClB,cAAc,CAAC,GAAG,aAAa,EAAE,EAAE,KAAK,MAAM,IAAI,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAOlD,MAAM,uBAAuB;AAE7B,MAAM,uBAAuB;AAE7B,MAAM,0BAA0B;AAAA;AAAA;AAAA;AAAA;", "names": []}