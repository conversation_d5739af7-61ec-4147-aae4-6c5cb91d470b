import "../../chunk-BUSYA2B4.js";
import nodeRuntime from "#safe-node-apis";
const throwMissingFsModule = (module) => {
  throw new Error(`Clerk: ${module} is missing. This is an internal error. Please contact Clerk's support.`);
};
const nodeFsOrThrow = () => {
  if (!nodeRuntime.fs) {
    throwMissingFsModule("fs");
  }
  return nodeRuntime.fs;
};
const nodePathOrThrow = () => {
  if (!nodeRuntime.path) {
    throwMissingFsModule("path");
  }
  return nodeRuntime.path;
};
const nodeCwdOrThrow = () => {
  if (!nodeRuntime.cwd) {
    throwMissingFsModule("cwd");
  }
  return nodeRuntime.cwd;
};
export {
  nodeCwdOrThrow,
  nodeFsOrThrow,
  nodePathOrThrow
};
//# sourceMappingURL=utils.js.map