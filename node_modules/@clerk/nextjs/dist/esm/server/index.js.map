{"version": 3, "sources": ["../../../src/server/index.ts"], "sourcesContent": ["/**\n * Generic exports\n */\nexport { createRouteMatcher } from './routeMatcher';\n\nexport { verifyToken, createClerkClient } from '@clerk/backend';\nexport { clerkClient } from './clerkClient';\n\n/**\n * Webhook-specific exports\n */\nexport type {\n  DeletedObjectJSON,\n  EmailJSON,\n  OrganizationJSON,\n  OrganizationDomainJSON,\n  OrganizationDomainVerificationJSON,\n  OrganizationInvitationJSON,\n  OrganizationMembershipJSON,\n  SessionJSON,\n  SMSMessageJSON,\n  UserJSON,\n  WaitlistEntryJSON,\n  WebhookEvent,\n  WebhookEventType,\n  UserWebhookEvent,\n  EmailWebhookEvent,\n  OrganizationWebhookEvent,\n  OrganizationDomainWebhookEvent,\n  OrganizationMembershipWebhookEvent,\n  OrganizationInvitationWebhookEvent,\n  PermissionWebhookEvent,\n  RoleWebhookEvent,\n  SessionWebhookEvent,\n  SMSWebhookEvent,\n  WaitlistEntryWebhookEvent,\n} from '@clerk/backend';\n\n/**\n * NextJS-specific exports\n */\nexport { getAuth } from './createGetAuth';\nexport { buildClerkProps } from './buildClerkProps';\nexport { auth } from '../app-router/server/auth';\nexport { currentUser } from '../app-router/server/currentUser';\nexport { clerkMiddleware } from './clerkMiddleware';\nexport type { ClerkMiddlewareAuth, ClerkMiddlewareAuthObject, ClerkMiddlewareOptions } from './clerkMiddleware';\n\n/**\n * Re-export resource types from @clerk/backend\n */\nexport type {\n  OrganizationMembershipRole,\n  // Resources\n  AllowlistIdentifier,\n  Client,\n  OrganizationMembership,\n  EmailAddress,\n  ExternalAccount,\n  Invitation,\n  OauthAccessToken,\n  Organization,\n  OrganizationInvitation,\n  OrganizationMembershipPublicUserData,\n  PhoneNumber,\n  Session,\n  SignInToken,\n  SMSMessage,\n  Token,\n  User,\n} from '@clerk/backend';\n\n/**\n * Utilities for reverification\n */\nexport { reverificationErrorResponse, reverificationError } from '@clerk/backend/internal';\n"], "mappings": ";AAGA,SAAS,0BAA0B;AAEnC,SAAS,aAAa,yBAAyB;AAC/C,SAAS,mBAAmB;AAmC5B,SAAS,eAAe;AACxB,SAAS,uBAAuB;AAChC,SAAS,YAAY;AACrB,SAAS,mBAAmB;AAC5B,SAAS,uBAAuB;AA8BhC,SAAS,6BAA6B,2BAA2B;", "names": []}