{"version": 3, "sources": ["../../../src/server/nextErrors.ts"], "sourcesContent": ["/**\n * Clerk's identifiers that are used alongside the ones from Next.js\n */\nconst CONTROL_FLOW_ERROR = {\n  REDIRECT_TO_URL: 'CLERK_PROTECT_REDIRECT_TO_URL',\n  REDIRECT_TO_SIGN_IN: 'CLERK_PROTECT_REDIRECT_TO_SIGN_IN',\n  REDIRECT_TO_SIGN_UP: 'CLERK_PROTECT_REDIRECT_TO_SIGN_UP',\n};\n\n/**\n * In-house implementation of `notFound()`\n * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/not-found.ts\n */\nconst LEGACY_NOT_FOUND_ERROR_CODE = 'NEXT_NOT_FOUND';\n\ntype LegacyNotFoundError = Error & {\n  digest: typeof LEGACY_NOT_FOUND_ERROR_CODE;\n};\n\n/**\n * Checks for the error thrown from `notFound()` for versions <= next@15.0.4\n */\nfunction isLegacyNextjsNotFoundError(error: unknown): error is LegacyNotFoundError {\n  if (typeof error !== 'object' || error === null || !('digest' in error)) {\n    return false;\n  }\n\n  return error.digest === LEGACY_NOT_FOUND_ERROR_CODE;\n}\n\nconst HTTPAccessErrorStatusCodes = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n};\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatusCodes));\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK';\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`;\n};\n\nexport function isHTTPAccessFallbackError(error: unknown): error is HTTPAccessFallbackError {\n  if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n    return false;\n  }\n  const [prefix, httpStatus] = error.digest.split(';');\n\n  return prefix === HTTP_ERROR_FALLBACK_ERROR_CODE && ALLOWED_CODES.has(Number(httpStatus));\n}\n\nexport function whichHTTPAccessFallbackError(error: unknown): number | undefined {\n  if (!isHTTPAccessFallbackError(error)) {\n    return undefined;\n  }\n\n  const [, httpStatus] = error.digest.split(';');\n  return Number(httpStatus);\n}\n\nfunction isNextjsNotFoundError(error: unknown): error is LegacyNotFoundError | HTTPAccessFallbackError {\n  return (\n    isLegacyNextjsNotFoundError(error) ||\n    // Checks for the error thrown from `notFound()` for canary versions of next@15\n    whichHTTPAccessFallbackError(error) === HTTPAccessErrorStatusCodes.NOT_FOUND\n  );\n}\n\n/**\n * In-house implementation of `redirect()` extended with a `clerk_digest` property\n * https://github.com/vercel/next.js/blob/canary/packages/next/src/client/components/redirect.ts\n */\n\nconst REDIRECT_ERROR_CODE = 'NEXT_REDIRECT';\n\ntype RedirectError<T = unknown> = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${'replace'};${string};${307};`;\n  clerk_digest: typeof CONTROL_FLOW_ERROR.REDIRECT_TO_URL | typeof CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN;\n} & T;\n\nfunction nextjsRedirectError(\n  url: string,\n  extra: Record<string, unknown>,\n  type: 'replace' = 'replace',\n  statusCode: 307 = 307,\n): never {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError;\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`;\n  error.clerk_digest = CONTROL_FLOW_ERROR.REDIRECT_TO_URL;\n  Object.assign(error, extra);\n  throw error;\n}\n\nfunction buildReturnBackUrl(url: string, returnBackUrl?: string | URL | null): string | URL {\n  return returnBackUrl === null ? '' : returnBackUrl || url;\n}\n\nfunction redirectToSignInError(url: string, returnBackUrl?: string | URL | null): never {\n  nextjsRedirectError(url, {\n    clerk_digest: CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN,\n    returnBackUrl: buildReturnBackUrl(url, returnBackUrl),\n  });\n}\n\nfunction redirectToSignUpError(url: string, returnBackUrl?: string | URL | null): never {\n  nextjsRedirectError(url, {\n    clerk_digest: CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_UP,\n    returnBackUrl: buildReturnBackUrl(url, returnBackUrl),\n  });\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nfunction isNextjsRedirectError(error: unknown): error is RedirectError<{ redirectUrl: string | URL }> {\n  if (typeof error !== 'object' || error === null || !('digest' in error) || typeof error.digest !== 'string') {\n    return false;\n  }\n\n  const digest = error.digest.split(';');\n  const [errorCode, type] = digest;\n  const destination = digest.slice(2, -2).join(';');\n  const status = digest.at(-2);\n\n  const statusCode = Number(status);\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode === 307\n  );\n}\n\nfunction isRedirectToSignInError(error: unknown): error is RedirectError<{ returnBackUrl: string | URL }> {\n  if (isNextjsRedirectError(error) && 'clerk_digest' in error) {\n    return error.clerk_digest === CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_IN;\n  }\n\n  return false;\n}\n\nfunction isRedirectToSignUpError(error: unknown): error is RedirectError<{ returnBackUrl: string | URL }> {\n  if (isNextjsRedirectError(error) && 'clerk_digest' in error) {\n    return error.clerk_digest === CONTROL_FLOW_ERROR.REDIRECT_TO_SIGN_UP;\n  }\n\n  return false;\n}\n\nexport {\n  isNextjsNotFoundError,\n  isLegacyNextjsNotFoundError,\n  redirectToSignInError,\n  redirectToSignUpError,\n  nextjsRedirectError,\n  isNextjsRedirectError,\n  isRedirectToSignInError,\n  isRedirectToSignUpError,\n};\n"], "mappings": ";AAGA,MAAM,qBAAqB;AAAA,EACzB,iBAAiB;AAAA,EACjB,qBAAqB;AAAA,EACrB,qBAAqB;AACvB;AAMA,MAAM,8BAA8B;AASpC,SAAS,4BAA4B,OAA8C;AACjF,MAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,EAAE,YAAY,QAAQ;AACvE,WAAO;AAAA,EACT;AAEA,SAAO,MAAM,WAAW;AAC1B;AAEA,MAAM,6BAA6B;AAAA,EACjC,WAAW;AAAA,EACX,WAAW;AAAA,EACX,cAAc;AAChB;AAEA,MAAM,gBAAgB,IAAI,IAAI,OAAO,OAAO,0BAA0B,CAAC;AAEhE,MAAM,iCAAiC;AAMvC,SAAS,0BAA0B,OAAkD;AAC1F,MAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,EAAE,YAAY,UAAU,OAAO,MAAM,WAAW,UAAU;AAC3G,WAAO;AAAA,EACT;AACA,QAAM,CAAC,QAAQ,UAAU,IAAI,MAAM,OAAO,MAAM,GAAG;AAEnD,SAAO,WAAW,kCAAkC,cAAc,IAAI,OAAO,UAAU,CAAC;AAC1F;AAEO,SAAS,6BAA6B,OAAoC;AAC/E,MAAI,CAAC,0BAA0B,KAAK,GAAG;AACrC,WAAO;AAAA,EACT;AAEA,QAAM,CAAC,EAAE,UAAU,IAAI,MAAM,OAAO,MAAM,GAAG;AAC7C,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,sBAAsB,OAAwE;AACrG,SACE,4BAA4B,KAAK;AAAA,EAEjC,6BAA6B,KAAK,MAAM,2BAA2B;AAEvE;AAOA,MAAM,sBAAsB;AAO5B,SAAS,oBACP,KACA,OACA,OAAkB,WAClB,aAAkB,KACX;AACP,QAAM,QAAQ,IAAI,MAAM,mBAAmB;AAC3C,QAAM,SAAS,GAAG,mBAAmB,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU;AAClE,QAAM,eAAe,mBAAmB;AACxC,SAAO,OAAO,OAAO,KAAK;AAC1B,QAAM;AACR;AAEA,SAAS,mBAAmB,KAAa,eAAmD;AAC1F,SAAO,kBAAkB,OAAO,KAAK,iBAAiB;AACxD;AAEA,SAAS,sBAAsB,KAAa,eAA4C;AACtF,sBAAoB,KAAK;AAAA,IACvB,cAAc,mBAAmB;AAAA,IACjC,eAAe,mBAAmB,KAAK,aAAa;AAAA,EACtD,CAAC;AACH;AAEA,SAAS,sBAAsB,KAAa,eAA4C;AACtF,sBAAoB,KAAK;AAAA,IACvB,cAAc,mBAAmB;AAAA,IACjC,eAAe,mBAAmB,KAAK,aAAa;AAAA,EACtD,CAAC;AACH;AASA,SAAS,sBAAsB,OAAuE;AACpG,MAAI,OAAO,UAAU,YAAY,UAAU,QAAQ,EAAE,YAAY,UAAU,OAAO,MAAM,WAAW,UAAU;AAC3G,WAAO;AAAA,EACT;AAEA,QAAM,SAAS,MAAM,OAAO,MAAM,GAAG;AACrC,QAAM,CAAC,WAAW,IAAI,IAAI;AAC1B,QAAM,cAAc,OAAO,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG;AAChD,QAAM,SAAS,OAAO,GAAG,EAAE;AAE3B,QAAM,aAAa,OAAO,MAAM;AAEhC,SACE,cAAc,wBACb,SAAS,aAAa,SAAS,WAChC,OAAO,gBAAgB,YACvB,CAAC,MAAM,UAAU,KACjB,eAAe;AAEnB;AAEA,SAAS,wBAAwB,OAAyE;AACxG,MAAI,sBAAsB,KAAK,KAAK,kBAAkB,OAAO;AAC3D,WAAO,MAAM,iBAAiB,mBAAmB;AAAA,EACnD;AAEA,SAAO;AACT;AAEA,SAAS,wBAAwB,OAAyE;AACxG,MAAI,sBAAsB,KAAK,KAAK,kBAAkB,OAAO;AAC3D,WAAO,MAAM,iBAAiB,mBAAmB;AAAA,EACnD;AAEA,SAAO;AACT;", "names": []}