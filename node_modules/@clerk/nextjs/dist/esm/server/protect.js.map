{"version": 3, "sources": ["../../../src/server/protect.ts"], "sourcesContent": ["import type { AuthObject } from '@clerk/backend';\nimport type { RedirectFun, SignedInAuthObject } from '@clerk/backend/internal';\nimport { constants } from '@clerk/backend/internal';\nimport type {\n  CheckAuthorizationFromSessionClaims,\n  CheckAuthorizationParamsFromSessionClaims,\n  CheckAuthorizationParamsWithCustomPermissions,\n  CheckAuthorizationWithCustomPermissions,\n  OrganizationCustomPermissionKey,\n} from '@clerk/types';\n\nimport { constants as nextConstants } from '../constants';\nimport { isNextFetcher } from './nextFetcher';\n\ntype AuthProtectOptions = {\n  /**\n   * The URL to redirect the user to if they are not authorized.\n   */\n  unauthorizedUrl?: string;\n  /**\n   * The URL to redirect the user to if they are not authenticated.\n   */\n  unauthenticatedUrl?: string;\n};\n\n/**\n * Throws a Nextjs notFound error if user is not authenticated or authorized.\n */\nexport interface AuthProtect {\n  <P extends OrganizationCustomPermissionKey>(\n    params?: CheckAuthorizationParamsFromSessionClaims<P>,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  (\n    params?: (has: CheckAuthorizationFromSessionClaims) => boolean,\n    options?: AuthProtectOptions,\n  ): Promise<SignedInAuthObject>;\n\n  (options?: AuthProtectOptions): Promise<SignedInAuthObject>;\n}\n\nexport function createProtect(opts: {\n  request: Request;\n  authObject: AuthObject;\n  /**\n   * middleware and pages throw a notFound error if signed out\n   * but the middleware needs to throw an error it can catch\n   * use this callback to customise the behavior\n   */\n  notFound: () => never;\n  /**\n   * see {@link notFound} above\n   */\n  redirect: (url: string) => void;\n  /**\n   * protect() in middleware redirects to signInUrl if signed out\n   * protect() in pages throws a notFound error if signed out\n   * use this callback to customise the behavior\n   */\n  redirectToSignIn: RedirectFun<unknown>;\n}): AuthProtect {\n  const { redirectToSignIn, authObject, redirect, notFound, request } = opts;\n\n  return (async (...args: any[]) => {\n    const optionValuesAsParam = args[0]?.unauthenticatedUrl || args[0]?.unauthorizedUrl;\n    const paramsOrFunction = optionValuesAsParam\n      ? undefined\n      : (args[0] as\n          | CheckAuthorizationParamsWithCustomPermissions\n          | ((has: CheckAuthorizationWithCustomPermissions) => boolean));\n    const unauthenticatedUrl = (args[0]?.unauthenticatedUrl || args[1]?.unauthenticatedUrl) as string | undefined;\n    const unauthorizedUrl = (args[0]?.unauthorizedUrl || args[1]?.unauthorizedUrl) as string | undefined;\n\n    const handleUnauthenticated = () => {\n      if (unauthenticatedUrl) {\n        return redirect(unauthenticatedUrl);\n      }\n      if (isPageRequest(request)) {\n        // TODO: Handle runtime values. What happens if runtime values are set in middleware and in ClerkProvider as well?\n        return redirectToSignIn();\n      }\n      return notFound();\n    };\n\n    const handleUnauthorized = () => {\n      if (unauthorizedUrl) {\n        return redirect(unauthorizedUrl);\n      }\n      return notFound();\n    };\n\n    /**\n     * Redirects the user back to the tasks URL if their session status is pending\n     */\n    if (authObject.sessionStatus === 'pending') {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is not authenticated\n     */\n    if (!authObject.userId) {\n      return handleUnauthenticated();\n    }\n\n    /**\n     * User is authenticated\n     */\n    if (!paramsOrFunction) {\n      return authObject;\n    }\n\n    /**\n     * if a function is passed and returns false then throw not found\n     */\n    if (typeof paramsOrFunction === 'function') {\n      if (paramsOrFunction(authObject.has)) {\n        return authObject;\n      }\n      return handleUnauthorized();\n    }\n\n    /**\n     * Checking if user is authorized when permission or role is passed\n     */\n    if (authObject.has(paramsOrFunction)) {\n      return authObject;\n    }\n\n    return handleUnauthorized();\n  }) as AuthProtect;\n}\n\nconst isServerActionRequest = (req: Request) => {\n  return (\n    !!req.headers.get(nextConstants.Headers.NextUrl) &&\n    (req.headers.get(constants.Headers.Accept)?.includes('text/x-component') ||\n      req.headers.get(constants.Headers.ContentType)?.includes('multipart/form-data') ||\n      !!req.headers.get(nextConstants.Headers.NextAction))\n  );\n};\n\nconst isPageRequest = (req: Request): boolean => {\n  return (\n    req.headers.get(constants.Headers.SecFetchDest) === 'document' ||\n    req.headers.get(constants.Headers.SecFetchDest) === 'iframe' ||\n    req.headers.get(constants.Headers.Accept)?.includes('text/html') ||\n    isAppRouterInternalNavigation(req) ||\n    isPagesRouterInternalNavigation(req)\n  );\n};\n\nconst isAppRouterInternalNavigation = (req: Request) =>\n  (!!req.headers.get(nextConstants.Headers.NextUrl) && !isServerActionRequest(req)) || isPagePathAvailable();\n\nconst isPagePathAvailable = () => {\n  const __fetch = globalThis.fetch;\n\n  if (!isNextFetcher(__fetch)) {\n    return false;\n  }\n\n  const { page, pagePath } = __fetch.__nextGetStaticStore().getStore() || {};\n\n  return Boolean(\n    // available on next@14\n    pagePath ||\n      // available on next@15\n      page,\n  );\n};\n\nconst isPagesRouterInternalNavigation = (req: Request) => !!req.headers.get(nextConstants.Headers.NextjsData);\n\n// /**\n//  * In case we want to handle router handlers and server actions differently in the future\n//  */\n// const isApiRouteRequest = (req: Request) => {\n//   return !isPageRequest(req) && !isServerActionRequest(req);\n// };\n"], "mappings": ";AAEA,SAAS,iBAAiB;AAS1B,SAAS,aAAa,qBAAqB;AAC3C,SAAS,qBAAqB;AA8BvB,SAAS,cAAc,MAmBd;AACd,QAAM,EAAE,kBAAkB,YAAY,UAAU,UAAU,QAAQ,IAAI;AAEtE,SAAQ,UAAU,SAAgB;AAhEpC;AAiEI,UAAM,wBAAsB,UAAK,CAAC,MAAN,mBAAS,yBAAsB,UAAK,CAAC,MAAN,mBAAS;AACpE,UAAM,mBAAmB,sBACrB,SACC,KAAK,CAAC;AAGX,UAAM,uBAAsB,UAAK,CAAC,MAAN,mBAAS,yBAAsB,UAAK,CAAC,MAAN,mBAAS;AACpE,UAAM,oBAAmB,UAAK,CAAC,MAAN,mBAAS,sBAAmB,UAAK,CAAC,MAAN,mBAAS;AAE9D,UAAM,wBAAwB,MAAM;AAClC,UAAI,oBAAoB;AACtB,eAAO,SAAS,kBAAkB;AAAA,MACpC;AACA,UAAI,cAAc,OAAO,GAAG;AAE1B,eAAO,iBAAiB;AAAA,MAC1B;AACA,aAAO,SAAS;AAAA,IAClB;AAEA,UAAM,qBAAqB,MAAM;AAC/B,UAAI,iBAAiB;AACnB,eAAO,SAAS,eAAe;AAAA,MACjC;AACA,aAAO,SAAS;AAAA,IAClB;AAKA,QAAI,WAAW,kBAAkB,WAAW;AAC1C,aAAO,sBAAsB;AAAA,IAC/B;AAKA,QAAI,CAAC,WAAW,QAAQ;AACtB,aAAO,sBAAsB;AAAA,IAC/B;AAKA,QAAI,CAAC,kBAAkB;AACrB,aAAO;AAAA,IACT;AAKA,QAAI,OAAO,qBAAqB,YAAY;AAC1C,UAAI,iBAAiB,WAAW,GAAG,GAAG;AACpC,eAAO;AAAA,MACT;AACA,aAAO,mBAAmB;AAAA,IAC5B;AAKA,QAAI,WAAW,IAAI,gBAAgB,GAAG;AACpC,aAAO;AAAA,IACT;AAEA,WAAO,mBAAmB;AAAA,EAC5B;AACF;AAEA,MAAM,wBAAwB,CAAC,QAAiB;AAtIhD;AAuIE,SACE,CAAC,CAAC,IAAI,QAAQ,IAAI,cAAc,QAAQ,OAAO,QAC9C,SAAI,QAAQ,IAAI,UAAU,QAAQ,MAAM,MAAxC,mBAA2C,SAAS,0BACnD,SAAI,QAAQ,IAAI,UAAU,QAAQ,WAAW,MAA7C,mBAAgD,SAAS,2BACzD,CAAC,CAAC,IAAI,QAAQ,IAAI,cAAc,QAAQ,UAAU;AAExD;AAEA,MAAM,gBAAgB,CAAC,QAA0B;AA/IjD;AAgJE,SACE,IAAI,QAAQ,IAAI,UAAU,QAAQ,YAAY,MAAM,cACpD,IAAI,QAAQ,IAAI,UAAU,QAAQ,YAAY,MAAM,cACpD,SAAI,QAAQ,IAAI,UAAU,QAAQ,MAAM,MAAxC,mBAA2C,SAAS,iBACpD,8BAA8B,GAAG,KACjC,gCAAgC,GAAG;AAEvC;AAEA,MAAM,gCAAgC,CAAC,QACpC,CAAC,CAAC,IAAI,QAAQ,IAAI,cAAc,QAAQ,OAAO,KAAK,CAAC,sBAAsB,GAAG,KAAM,oBAAoB;AAE3G,MAAM,sBAAsB,MAAM;AAChC,QAAM,UAAU,WAAW;AAE3B,MAAI,CAAC,cAAc,OAAO,GAAG;AAC3B,WAAO;AAAA,EACT;AAEA,QAAM,EAAE,MAAM,SAAS,IAAI,QAAQ,qBAAqB,EAAE,SAAS,KAAK,CAAC;AAEzE,SAAO;AAAA;AAAA,IAEL;AAAA,IAEE;AAAA,EACJ;AACF;AAEA,MAAM,kCAAkC,CAAC,QAAiB,CAAC,CAAC,IAAI,QAAQ,IAAI,cAAc,QAAQ,UAAU;", "names": []}