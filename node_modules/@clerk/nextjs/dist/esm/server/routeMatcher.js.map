{"version": 3, "sources": ["../../../src/server/routeMatcher.ts"], "sourcesContent": ["import { createPathMatcher, type WithPathPatternWildcard } from '@clerk/shared/pathMatcher';\nimport type { Autocomplete } from '@clerk/types';\nimport type Link from 'next/link';\nimport type { NextRequest } from 'next/server';\n\ntype NextTypedRoute<T = Parameters<typeof Link>['0']['href']> = T extends string ? T : never;\ntype RouteMatcherWithNextTypedRoutes = Autocomplete<WithPathPatternWildcard<NextTypedRoute> | NextTypedRoute>;\n\nexport type RouteMatcherParam =\n  | Array<RegExp | RouteMatcherWithNextTypedRoutes>\n  | RegExp\n  | RouteMatcherWithNextTypedRoutes\n  | ((req: NextRequest) => boolean);\n\n/**\n * Returns a function that accepts a `Request` object and returns whether the request matches the list of\n * predefined routes that can be passed in as the first argument.\n *\n * You can use glob patterns to match multiple routes or a function to match against the request object.\n * Path patterns and regular expressions are supported, for example: `['/foo', '/bar(.*)'] or `[/^\\/foo\\/.*$/]`\n * For more information, see: https://clerk.com/docs\n */\nexport const createRouteMatcher = (routes: RouteMatcherParam) => {\n  if (typeof routes === 'function') {\n    return (req: NextRequest) => routes(req);\n  }\n\n  const matcher = createPathMatcher(routes);\n  return (req: NextRequest) => matcher(req.nextUrl.pathname);\n};\n"], "mappings": ";AAAA,SAAS,yBAAuD;AAsBzD,MAAM,qBAAqB,CAAC,WAA8B;AAC/D,MAAI,OAAO,WAAW,YAAY;AAChC,WAAO,CAAC,QAAqB,OAAO,GAAG;AAAA,EACzC;AAEA,QAAM,UAAU,kBAAkB,MAAM;AACxC,SAAO,CAAC,QAAqB,QAAQ,IAAI,QAAQ,QAAQ;AAC3D;", "names": []}