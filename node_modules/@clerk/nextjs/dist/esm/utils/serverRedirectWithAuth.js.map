{"version": 3, "sources": ["../../../src/utils/serverRedirectWithAuth.ts"], "sourcesContent": ["// Middleware runs on the server side, before clerk-js is loaded, that's why we need Cookies.\nimport type { ClerkRequest } from '@clerk/backend/internal';\nimport { constants } from '@clerk/backend/internal';\nimport { DEV_BROWSER_JWT_KEY, setDevBrowserJWTInURL } from '@clerk/shared/devBrowser';\nimport { isDevelopmentFromSecretKey } from '@clerk/shared/keys';\nimport { NextResponse } from 'next/server';\n\n/**\n * Grabs the dev browser JWT from cookies and appends it to the redirect URL when redirecting to cross-origin.\n */\nexport const serverRedirectWithAuth = (clerkRequest: ClerkRequest, res: Response, opts: { secretKey: string }) => {\n  const location = res.headers.get('location');\n  const shouldAppendDevBrowser = res.headers.get(constants.Headers.ClerkRedirectTo) === 'true';\n\n  if (\n    shouldAppendDevBrowser &&\n    !!location &&\n    isDevelopmentFromSecretKey(opts.secretKey) &&\n    clerkRequest.clerkUrl.isCrossOrigin(location)\n  ) {\n    const dbJwt = clerkRequest.cookies.get(DEV_BROWSER_JWT_KEY) || '';\n    // Next.js 12.1+ allows redirects only to absolute URLs\n    const url = new URL(location);\n    const urlWithDevBrowser = setDevBrowserJWTInURL(url, dbJwt);\n    return NextResponse.redirect(urlWithDevBrowser.href, res);\n  }\n  return res;\n};\n"], "mappings": ";AAEA,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB,6BAA6B;AAC3D,SAAS,kCAAkC;AAC3C,SAAS,oBAAoB;AAKtB,MAAM,yBAAyB,CAAC,cAA4B,KAAe,SAAgC;AAChH,QAAM,WAAW,IAAI,QAAQ,IAAI,UAAU;AAC3C,QAAM,yBAAyB,IAAI,QAAQ,IAAI,UAAU,QAAQ,eAAe,MAAM;AAEtF,MACE,0BACA,CAAC,CAAC,YACF,2BAA2B,KAAK,SAAS,KACzC,aAAa,SAAS,cAAc,QAAQ,GAC5C;AACA,UAAM,QAAQ,aAAa,QAAQ,IAAI,mBAAmB,KAAK;AAE/D,UAAM,MAAM,IAAI,IAAI,QAAQ;AAC5B,UAAM,oBAAoB,sBAAsB,KAAK,KAAK;AAC1D,WAAO,aAAa,SAAS,kBAAkB,MAAM,GAAG;AAAA,EAC1D;AACA,SAAO;AACT;", "names": []}