var kt=Object.defineProperty;var bt=(c,t,s)=>t in c?kt(c,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):c[t]=s;var it=(c,t,s)=>bt(c,typeof t!="symbol"?t+"":t,s);var lt,ht,dt,pt,xt,_t,at=((lt=typeof globalThis!="undefined"?globalThis:void 0)==null?void 0:lt.crypto)||((ht=typeof global!="undefined"?global:void 0)==null?void 0:ht.crypto)||((dt=typeof window!="undefined"?window:void 0)==null?void 0:dt.crypto)||((pt=typeof self!="undefined"?self:void 0)==null?void 0:pt.crypto)||((_t=(xt=typeof frames!="undefined"?frames:void 0)==null?void 0:xt[0])==null?void 0:_t.crypto),Z;at?Z=c=>{let t=[];for(let s=0,e;s<c;s+=4)t.push(at.getRandomValues(new Uint32Array(1))[0]);return new u(t,c)}:Z=c=>{let t=[],s=e=>{let r=e,o=987654321,n=4294967295;return()=>{o=36969*(o&65535)+(o>>16)&n,r=18e3*(r&65535)+(r>>16)&n;let h=(o<<16)+r&n;return h/=4294967296,h+=.5,h*(Math.random()>.5?1:-1)}};for(let e=0,r;e<c;e+=4){let o=s((r||Math.random())*4294967296);r=o()*987654071,t.push(o()*4294967296|0)}return new u(t,c)};var m=class{static create(...t){return new this(...t)}mixIn(t){return Object.assign(this,t)}clone(){let t=new this.constructor;return Object.assign(t,this),t}},u=class extends m{constructor(t=[],s=t.length*4){super();let e=t;if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){let r=e.byteLength,o=[];for(let n=0;n<r;n+=1)o[n>>>2]|=e[n]<<24-n%4*8;this.words=o,this.sigBytes=r}else this.words=t,this.sigBytes=s}toString(t=Mt){return t.stringify(this)}concat(t){let s=this.words,e=t.words,r=this.sigBytes,o=t.sigBytes;if(this.clamp(),r%4)for(let n=0;n<o;n+=1){let h=e[n>>>2]>>>24-n%4*8&255;s[r+n>>>2]|=h<<24-(r+n)%4*8}else for(let n=0;n<o;n+=4)s[r+n>>>2]=e[n>>>2];return this.sigBytes+=o,this}clamp(){let{words:t,sigBytes:s}=this;t[s>>>2]&=4294967295<<32-s%4*8,t.length=Math.ceil(s/4)}clone(){let t=super.clone.call(this);return t.words=this.words.slice(0),t}};it(u,"random",Z);var Mt={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push((o>>>4).toString(16)),e.push((o&15).toString(16))}return e.join("")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=2)s[e>>>3]|=parseInt(c.substr(e,2),16)<<24-e%8*4;return new u(s,t/2)}},ft={stringify(c){let{words:t,sigBytes:s}=c,e=[];for(let r=0;r<s;r+=1){let o=t[r>>>2]>>>24-r%4*8&255;e.push(String.fromCharCode(o))}return e.join("")},parse(c){let t=c.length,s=[];for(let e=0;e<t;e+=1)s[e>>>2]|=(c.charCodeAt(e)&255)<<24-e%4*8;return new u(s,t)}},X={stringify(c){try{return decodeURIComponent(escape(ft.stringify(c)))}catch{throw new Error("Malformed UTF-8 data")}},parse(c){return ft.parse(unescape(encodeURIComponent(c)))}},N=class extends m{constructor(){super(),this._minBufferSize=0}reset(){this._data=new u,this._nDataBytes=0}_append(t){let s=t;typeof s=="string"&&(s=X.parse(s)),this._data.concat(s),this._nDataBytes+=s.sigBytes}_process(t){let s,{_data:e,blockSize:r}=this,o=e.words,n=e.sigBytes,h=r*4,x=n/h;t?x=Math.ceil(x):x=Math.max((x|0)-this._minBufferSize,0);let p=x*r,_=Math.min(p*4,n);if(p){for(let y=0;y<p;y+=r)this._doProcessBlock(o,y);s=o.splice(0,p),e.sigBytes-=_}return new u(s,_)}clone(){let t=super.clone.call(this);return t._data=this._data.clone(),t}},H=class extends N{constructor(t){super(),this.blockSize=512/32,this.cfg=Object.assign(new m,t),this.reset()}static _createHelper(t){return(s,e)=>new t(e).finalize(s)}static _createHmacHelper(t){return(s,e)=>new $(t,e).finalize(s)}reset(){super.reset.call(this),this._doReset()}update(t){return this._append(t),this._process(),this}finalize(t){return t&&this._append(t),this._doFinalize()}},$=class extends m{constructor(t,s){super();let e=new t;this._hasher=e;let r=s;typeof r=="string"&&(r=X.parse(r));let o=e.blockSize,n=o*4;r.sigBytes>n&&(r=e.finalize(s)),r.clamp();let h=r.clone();this._oKey=h;let x=r.clone();this._iKey=x;let p=h.words,_=x.words;for(let y=0;y<o;y+=1)p[y]^=1549556828,_[y]^=909522486;h.sigBytes=n,x.sigBytes=n,this.reset()}reset(){let t=this._hasher;t.reset(),t.update(this._iKey)}update(t){return this._hasher.update(t),this}finalize(t){let s=this._hasher,e=s.finalize(t);return s.reset(),s.finalize(this._oKey.clone().concat(e))}};var zt=(c,t,s)=>{let e=[],r=0;for(let o=0;o<t;o+=1)if(o%4){let n=s[c.charCodeAt(o-1)]<<o%4*2,h=s[c.charCodeAt(o)]>>>6-o%4*2,x=n|h;e[r>>>2]|=x<<24-r%4*8,r+=1}return u.create(e,r)},tt={stringify(c){let{words:t,sigBytes:s}=c,e=this._map;c.clamp();let r=[];for(let n=0;n<s;n+=3){let h=t[n>>>2]>>>24-n%4*8&255,x=t[n+1>>>2]>>>24-(n+1)%4*8&255,p=t[n+2>>>2]>>>24-(n+2)%4*8&255,_=h<<16|x<<8|p;for(let y=0;y<4&&n+y*.75<s;y+=1)r.push(e.charAt(_>>>6*(3-y)&63))}let o=e.charAt(64);if(o)for(;r.length%4;)r.push(o);return r.join("")},parse(c){let t=c.length,s=this._map,e=this._reverseMap;if(!e){this._reverseMap=[],e=this._reverseMap;for(let o=0;o<s.length;o+=1)e[s.charCodeAt(o)]=o}let r=s.charAt(64);if(r){let o=c.indexOf(r);o!==-1&&(t=o)}return zt(c,t,e)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};var d=[];for(let c=0;c<64;c+=1)d[c]=Math.abs(Math.sin(c+1))*4294967296|0;var w=(c,t,s,e,r,o,n)=>{let h=c+(t&s|~t&e)+r+n;return(h<<o|h>>>32-o)+t},B=(c,t,s,e,r,o,n)=>{let h=c+(t&e|s&~e)+r+n;return(h<<o|h>>>32-o)+t},k=(c,t,s,e,r,o,n)=>{let h=c+(t^s^e)+r+n;return(h<<o|h>>>32-o)+t},b=(c,t,s,e,r,o,n)=>{let h=c+(s^(t|~e))+r+n;return(h<<o|h>>>32-o)+t},L=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878])}_doProcessBlock(t,s){let e=t;for(let Y=0;Y<16;Y+=1){let ct=s+Y,G=t[ct];e[ct]=(G<<8|G>>>24)&16711935|(G<<24|G>>>8)&4278255360}let r=this._hash.words,o=e[s+0],n=e[s+1],h=e[s+2],x=e[s+3],p=e[s+4],_=e[s+5],y=e[s+6],M=e[s+7],z=e[s+8],v=e[s+9],g=e[s+10],O=e[s+11],S=e[s+12],P=e[s+13],I=e[s+14],W=e[s+15],i=r[0],a=r[1],f=r[2],l=r[3];i=w(i,a,f,l,o,7,d[0]),l=w(l,i,a,f,n,12,d[1]),f=w(f,l,i,a,h,17,d[2]),a=w(a,f,l,i,x,22,d[3]),i=w(i,a,f,l,p,7,d[4]),l=w(l,i,a,f,_,12,d[5]),f=w(f,l,i,a,y,17,d[6]),a=w(a,f,l,i,M,22,d[7]),i=w(i,a,f,l,z,7,d[8]),l=w(l,i,a,f,v,12,d[9]),f=w(f,l,i,a,g,17,d[10]),a=w(a,f,l,i,O,22,d[11]),i=w(i,a,f,l,S,7,d[12]),l=w(l,i,a,f,P,12,d[13]),f=w(f,l,i,a,I,17,d[14]),a=w(a,f,l,i,W,22,d[15]),i=B(i,a,f,l,n,5,d[16]),l=B(l,i,a,f,y,9,d[17]),f=B(f,l,i,a,O,14,d[18]),a=B(a,f,l,i,o,20,d[19]),i=B(i,a,f,l,_,5,d[20]),l=B(l,i,a,f,g,9,d[21]),f=B(f,l,i,a,W,14,d[22]),a=B(a,f,l,i,p,20,d[23]),i=B(i,a,f,l,v,5,d[24]),l=B(l,i,a,f,I,9,d[25]),f=B(f,l,i,a,x,14,d[26]),a=B(a,f,l,i,z,20,d[27]),i=B(i,a,f,l,P,5,d[28]),l=B(l,i,a,f,h,9,d[29]),f=B(f,l,i,a,M,14,d[30]),a=B(a,f,l,i,S,20,d[31]),i=k(i,a,f,l,_,4,d[32]),l=k(l,i,a,f,z,11,d[33]),f=k(f,l,i,a,O,16,d[34]),a=k(a,f,l,i,I,23,d[35]),i=k(i,a,f,l,n,4,d[36]),l=k(l,i,a,f,p,11,d[37]),f=k(f,l,i,a,M,16,d[38]),a=k(a,f,l,i,g,23,d[39]),i=k(i,a,f,l,P,4,d[40]),l=k(l,i,a,f,o,11,d[41]),f=k(f,l,i,a,x,16,d[42]),a=k(a,f,l,i,y,23,d[43]),i=k(i,a,f,l,v,4,d[44]),l=k(l,i,a,f,S,11,d[45]),f=k(f,l,i,a,W,16,d[46]),a=k(a,f,l,i,h,23,d[47]),i=b(i,a,f,l,o,6,d[48]),l=b(l,i,a,f,M,10,d[49]),f=b(f,l,i,a,I,15,d[50]),a=b(a,f,l,i,_,21,d[51]),i=b(i,a,f,l,S,6,d[52]),l=b(l,i,a,f,x,10,d[53]),f=b(f,l,i,a,g,15,d[54]),a=b(a,f,l,i,n,21,d[55]),i=b(i,a,f,l,z,6,d[56]),l=b(l,i,a,f,W,10,d[57]),f=b(f,l,i,a,y,15,d[58]),a=b(a,f,l,i,P,21,d[59]),i=b(i,a,f,l,p,6,d[60]),l=b(l,i,a,f,O,10,d[61]),f=b(f,l,i,a,h,15,d[62]),a=b(a,f,l,i,v,21,d[63]),r[0]=r[0]+i|0,r[1]=r[1]+a|0,r[2]=r[2]+f|0,r[3]=r[3]+l|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;s[r>>>5]|=128<<24-r%32;let o=Math.floor(e/4294967296),n=e;s[(r+64>>>9<<4)+15]=(o<<8|o>>>24)&16711935|(o<<24|o>>>8)&4278255360,s[(r+64>>>9<<4)+14]=(n<<8|n>>>24)&16711935|(n<<24|n>>>8)&4278255360,t.sigBytes=(s.length+1)*4,this._process();let h=this._hash,x=h.words;for(let p=0;p<4;p+=1){let _=x[p];x[p]=(_<<8|_>>>24)&16711935|(_<<24|_>>>8)&4278255360}return h}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},St=H._createHelper(L),Pt=H._createHmacHelper(L);var T=class extends m{constructor(t){super(),this.cfg=Object.assign(new m,{keySize:128/32,hasher:L,iterations:1},t)}compute(t,s){let e,{cfg:r}=this,o=r.hasher.create(),n=u.create(),h=n.words,{keySize:x,iterations:p}=r;for(;h.length<x;){e&&o.update(e),e=o.update(t).finalize(s),o.reset();for(let _=1;_<p;_+=1)e=o.finalize(e),o.reset();n.concat(e)}return n.sigBytes=x*4,n}};var C=class extends N{constructor(t,s,e){super(),this.cfg=Object.assign(new m,e),this._xformMode=t,this._key=s,this.reset()}static createEncryptor(t,s){return this.create(this._ENC_XFORM_MODE,t,s)}static createDecryptor(t,s){return this.create(this._DEC_XFORM_MODE,t,s)}static _createHelper(t){let s=e=>typeof e=="string"?q:E;return{encrypt(e,r,o){return s(r).encrypt(t,e,r,o)},decrypt(e,r,o){return s(r).decrypt(t,e,r,o)}}}reset(){super.reset.call(this),this._doReset()}process(t){return this._append(t),this._process()}finalize(t){return t&&this._append(t),this._doFinalize()}};C._ENC_XFORM_MODE=1;C._DEC_XFORM_MODE=2;C.keySize=128/32;C.ivSize=128/32;var et=class extends m{constructor(t,s){super(),this._cipher=t,this._iv=s}static createEncryptor(t,s){return this.Encryptor.create(t,s)}static createDecryptor(t,s){return this.Decryptor.create(t,s)}};function yt(c,t,s){let e=c,r,o=this._iv;o?(r=o,this._iv=void 0):r=this._prevBlock;for(let n=0;n<s;n+=1)e[t+n]^=r[n]}var j=class extends et{};j.Encryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s;yt.call(this,c,t,e),s.encryptBlock(c,t),this._prevBlock=c.slice(t,t+e)}};j.Decryptor=class extends j{processBlock(c,t){let s=this._cipher,{blockSize:e}=s,r=c.slice(t,t+e);s.decryptBlock(c,t),yt.call(this,c,t,e),this._prevBlock=r}};var vt={pad(c,t){let s=t*4,e=s-c.sigBytes%s,r=e<<24|e<<16|e<<8|e,o=[];for(let h=0;h<e;h+=4)o.push(r);let n=u.create(o,e);c.concat(n)},unpad(c){let t=c,s=t.words[t.sigBytes-1>>>2]&255;t.sigBytes-=s}},U=class extends C{constructor(t,s,e){super(t,s,Object.assign({mode:j,padding:vt},e)),this.blockSize=128/32}reset(){let t;super.reset.call(this);let{cfg:s}=this,{iv:e,mode:r}=s;this._xformMode===this.constructor._ENC_XFORM_MODE?t=r.createEncryptor:(t=r.createDecryptor,this._minBufferSize=1),this._mode=t.call(r,this,e&&e.words),this._mode.__creator=t}_doProcessBlock(t,s){this._mode.processBlock(t,s)}_doFinalize(){let t,{padding:s}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(s.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),s.unpad(t)),t}},V=class extends m{constructor(t){super(),this.mixIn(t)}toString(t){return(t||this.formatter).stringify(this)}},Rt={stringify(c){let t,{ciphertext:s,salt:e}=c;return e?t=u.create([1398893684,1701076831]).concat(e).concat(s):t=s,t.toString(tt)},parse(c){let t,s=tt.parse(c),e=s.words;return e[0]===1398893684&&e[1]===1701076831&&(t=u.create(e.slice(2,4)),e.splice(0,4),s.sigBytes-=16),V.create({ciphertext:s,salt:t})}},E=class extends m{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=t.createEncryptor(e,o),h=n.finalize(s),x=n.cfg;return V.create({ciphertext:h,key:e,iv:x.iv,algorithm:t,mode:x.mode,padding:x.padding,blockSize:n.blockSize,formatter:o.format})}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);return o=this._parse(o,n.format),t.createDecryptor(e,n).finalize(o.ciphertext)}static _parse(t,s){return typeof t=="string"?s.parse(t,this):t}};E.cfg=Object.assign(new m,{format:Rt});var Ft={execute(c,t,s,e,r){let o=e;o||(o=u.random(64/8));let n;r?n=T.create({keySize:t+s,hasher:r}).compute(c,o):n=T.create({keySize:t+s}).compute(c,o);let h=u.create(n.words.slice(t),s*4);return n.sigBytes=t*4,V.create({key:n,iv:h,salt:o})}},q=class extends E{static encrypt(t,s,e,r){let o=Object.assign(new m,this.cfg,r),n=o.kdf.execute(e,t.keySize,t.ivSize,o.salt,o.hasher);o.iv=n.iv;let h=E.encrypt.call(this,t,s,n.key,o);return h.mixIn(n),h}static decrypt(t,s,e,r){let o=s,n=Object.assign(new m,this.cfg,r);o=this._parse(o,n.format);let h=n.kdf.execute(e,t.keySize,t.ivSize,o.salt,n.hasher);return n.iv=h.iv,E.decrypt.call(this,t,o,h.key,n)}};q.cfg=Object.assign(E.cfg,{kdf:Ft});var R=[],ut=[],gt=[],mt=[],wt=[],Bt=[],st=[],rt=[],ot=[],nt=[],A=[];for(let c=0;c<256;c+=1)c<128?A[c]=c<<1:A[c]=c<<1^283;var F=0,D=0;for(let c=0;c<256;c+=1){let t=D^D<<1^D<<2^D<<3^D<<4;t=t>>>8^t&255^99,R[F]=t,ut[t]=F;let s=A[F],e=A[s],r=A[e],o=A[t]*257^t*16843008;gt[F]=o<<24|o>>>8,mt[F]=o<<16|o>>>16,wt[F]=o<<8|o>>>24,Bt[F]=o,o=r*16843009^e*65537^s*257^F*16843008,st[t]=o<<24|o>>>8,rt[t]=o<<16|o>>>16,ot[t]=o<<8|o>>>24,nt[t]=o,F?(F=s^A[A[A[r^s]]],D^=A[A[D]]):(D=1,F=D)}var At=[0,1,2,4,8,16,32,64,128,27,54],J=class extends U{_doReset(){let t;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let s=this._keyPriorReset,e=s.words,r=s.sigBytes/4;this._nRounds=r+6;let n=(this._nRounds+1)*4;this._keySchedule=[];let h=this._keySchedule;for(let p=0;p<n;p+=1)p<r?h[p]=e[p]:(t=h[p-1],p%r?r>6&&p%r===4&&(t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255]):(t=t<<8|t>>>24,t=R[t>>>24]<<24|R[t>>>16&255]<<16|R[t>>>8&255]<<8|R[t&255],t^=At[p/r|0]<<24),h[p]=h[p-r]^t);this._invKeySchedule=[];let x=this._invKeySchedule;for(let p=0;p<n;p+=1){let _=n-p;p%4?t=h[_]:t=h[_-4],p<4||_<=4?x[p]=t:x[p]=st[R[t>>>24]]^rt[R[t>>>16&255]]^ot[R[t>>>8&255]]^nt[R[t&255]]}}encryptBlock(t,s){this._doCryptBlock(t,s,this._keySchedule,gt,mt,wt,Bt,R)}decryptBlock(t,s){let e=t,r=e[s+1];e[s+1]=e[s+3],e[s+3]=r,this._doCryptBlock(e,s,this._invKeySchedule,st,rt,ot,nt,ut),r=e[s+1],e[s+1]=e[s+3],e[s+3]=r}_doCryptBlock(t,s,e,r,o,n,h,x){let p=t,_=this._nRounds,y=p[s]^e[0],M=p[s+1]^e[1],z=p[s+2]^e[2],v=p[s+3]^e[3],g=4;for(let W=1;W<_;W+=1){let i=r[y>>>24]^o[M>>>16&255]^n[z>>>8&255]^h[v&255]^e[g];g+=1;let a=r[M>>>24]^o[z>>>16&255]^n[v>>>8&255]^h[y&255]^e[g];g+=1;let f=r[z>>>24]^o[v>>>16&255]^n[y>>>8&255]^h[M&255]^e[g];g+=1;let l=r[v>>>24]^o[y>>>16&255]^n[M>>>8&255]^h[z&255]^e[g];g+=1,y=i,M=a,z=f,v=l}let O=(x[y>>>24]<<24|x[M>>>16&255]<<16|x[z>>>8&255]<<8|x[v&255])^e[g];g+=1;let S=(x[M>>>24]<<24|x[z>>>16&255]<<16|x[v>>>8&255]<<8|x[y&255])^e[g];g+=1;let P=(x[z>>>24]<<24|x[v>>>16&255]<<16|x[y>>>8&255]<<8|x[M&255])^e[g];g+=1;let I=(x[v>>>24]<<24|x[y>>>16&255]<<16|x[M>>>8&255]<<8|x[z&255])^e[g];g+=1,p[s]=O,p[s+1]=S,p[s+2]=P,p[s+3]=I}};J.keySize=256/32;var Ht=U._createHelper(J);var K=[],Q=class extends H{_doReset(){this._hash=new u([1732584193,4023233417,2562383102,271733878,3285377520])}_doProcessBlock(t,s){let e=this._hash.words,r=e[0],o=e[1],n=e[2],h=e[3],x=e[4];for(let p=0;p<80;p+=1){if(p<16)K[p]=t[s+p]|0;else{let y=K[p-3]^K[p-8]^K[p-14]^K[p-16];K[p]=y<<1|y>>>31}let _=(r<<5|r>>>27)+x+K[p];p<20?_+=(o&n|~o&h)+1518500249:p<40?_+=(o^n^h)+1859775393:p<60?_+=(o&n|o&h|n&h)-1894007588:_+=(o^n^h)-899497514,x=h,h=n,n=o<<30|o>>>2,o=r,r=_}e[0]=e[0]+r|0,e[1]=e[1]+o|0,e[2]=e[2]+n|0,e[3]=e[3]+h|0,e[4]=e[4]+x|0}_doFinalize(){let t=this._data,s=t.words,e=this._nDataBytes*8,r=t.sigBytes*8;return s[r>>>5]|=128<<24-r%32,s[(r+64>>>9<<4)+14]=Math.floor(e/4294967296),s[(r+64>>>9<<4)+15]=e,t.sigBytes=s.length*4,this._process(),this._hash}clone(){let t=super.clone.call(this);return t._hash=this._hash.clone(),t}},Xt=H._createHelper(Q),Dt=H._createHmacHelper(Q);export{Ht as AES,Dt as HmacSHA1,X as Utf8};
