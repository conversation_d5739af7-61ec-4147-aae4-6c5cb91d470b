{"version": 3, "sources": ["../src/getEnvVariable.ts"], "sourcesContent": ["type CloudflareEnv = { env: Record<string, string> };\n\nconst hasCloudflareProxyContext = (context: any): context is { cloudflare: CloudflareEnv } => {\n  return !!context?.cloudflare?.env;\n};\n\nconst hasCloudflareContext = (context: any): context is CloudflareEnv => {\n  return !!context?.env;\n};\n\n/**\n * Retrieves an environment variable across runtime environments.\n * @param name - The environment variable name to retrieve\n * @param context - Optional context object that may contain environment values\n * @returns The environment variable value or empty string if not found\n */\nexport const getEnvVariable = (name: string, context?: Record<string, any>): string => {\n  // Node envs\n  if (typeof process !== 'undefined' && process.env && typeof process.env[name] === 'string') {\n    return process.env[name];\n  }\n\n  // Vite specific\n  if (typeof import.meta !== 'undefined' && import.meta.env && typeof import.meta.env[name] === 'string') {\n    return import.meta.env[name];\n  }\n\n  if (hasCloudflareProxyContext(context)) {\n    return context.cloudflare.env[name] || '';\n  }\n\n  // Cloudflare\n  if (hasCloudflareContext(context)) {\n    return context.env[name] || '';\n  }\n\n  // Check whether the value exists in the context object directly\n  if (context && typeof context[name] === 'string') {\n    return context[name];\n  }\n\n  // Cloudflare workers\n  try {\n    return globalThis[name as keyof typeof globalThis];\n  } catch {\n    // This will raise an error in Cloudflare Pages\n  }\n\n  return '';\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,IAAM,4BAA4B,CAAC,YAA2D;AAC5F,SAAO,CAAC,CAAC,SAAS,YAAY;AAChC;AAEA,IAAM,uBAAuB,CAAC,YAA2C;AACvE,SAAO,CAAC,CAAC,SAAS;AACpB;AAQO,IAAM,iBAAiB,CAAC,MAAc,YAA0C;AAErF,MAAI,OAAO,YAAY,eAAe,QAAQ,OAAO,OAAO,QAAQ,IAAI,IAAI,MAAM,UAAU;AAC1F,WAAO,QAAQ,IAAI,IAAI;AAAA,EACzB;AAGA,MAAI,OAAO,gBAAgB,eAAe,YAAY,OAAO,OAAO,YAAY,IAAI,IAAI,MAAM,UAAU;AACtG,WAAO,YAAY,IAAI,IAAI;AAAA,EAC7B;AAEA,MAAI,0BAA0B,OAAO,GAAG;AACtC,WAAO,QAAQ,WAAW,IAAI,IAAI,KAAK;AAAA,EACzC;AAGA,MAAI,qBAAqB,OAAO,GAAG;AACjC,WAAO,QAAQ,IAAI,IAAI,KAAK;AAAA,EAC9B;AAGA,MAAI,WAAW,OAAO,QAAQ,IAAI,MAAM,UAAU;AAChD,WAAO,QAAQ,IAAI;AAAA,EACrB;AAGA,MAAI;AACF,WAAO,WAAW,IAA+B;AAAA,EACnD,QAAQ;AAAA,EAER;AAEA,SAAO;AACT;", "names": []}