"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("react"),r=require("three"),t=require("zustand");function a(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var a=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,a.get?a:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var o=a(e);let n=0;const s=t.create((e=>(r.DefaultLoadingManager.onStart=(r,t,a)=>{e({active:!0,item:r,loaded:t,total:a,progress:(t-n)/(a-n)*100})},r.DefaultLoadingManager.onLoad=()=>{e({active:!1})},r.DefaultLoadingManager.onError=r=>e((e=>({errors:[...e.errors,r]}))),r.<PERSON>faultLoadingManager.onProgress=(r,t,a)=>{t===a&&(n=a),e({active:!0,item:r,loaded:t,total:a,progress:(t-n)/(a-n)*100||100})},{errors:[],active:!1,progress:0,item:"",loaded:0,total:0})));exports.Progress=function({children:e}){const r=s();return o.createElement(o.Fragment,null,null==e?void 0:e(r))},exports.useProgress=s;
