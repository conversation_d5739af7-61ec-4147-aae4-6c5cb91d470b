"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),n=require("@react-three/fiber");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function u(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var i=a(e),o=u(r),c=u(t);const l=c.forwardRef((({children:e,compute:r,renderPriority:t=-1,eventPriority:a=0,frames:u=1/0,stencilBuffer:l=!1,depthBuffer:f=!0,generateMipmaps:p=!1,resolution:m=896,near:d=.1,far:b=1e3,flip:x=!1,position:g,rotation:h,scale:y,quaternion:v,matrix:P,matrixAutoUpdate:j,...E},O)=>{const{size:q,viewport:w}=n.useThree(),M=c.useRef(null),T=c.useMemo((()=>{const e=new o.WebGLCubeRenderTarget(Math.max((m||q.width)*w.dpr,(m||q.height)*w.dpr),{stencilBuffer:l,depthBuffer:f,generateMipmaps:p});return e.texture.isRenderTargetTexture=!x,e.texture.flipY=!0,e.texture.type=o.HalfFloatType,e}),[m,x]);c.useEffect((()=>()=>T.dispose()),[T]);const[R]=c.useState((()=>new o.Scene));return c.useImperativeHandle(O,(()=>({scene:R,fbo:T,camera:M.current})),[T]),c.createElement(c.Fragment,null,n.createPortal(c.createElement(s,{renderPriority:t,frames:u,camera:M},e,c.createElement("group",{onPointerOver:()=>null})),R,{events:{compute:r,priority:a}}),c.createElement("primitive",i.default({object:T.texture},E)),c.createElement("cubeCamera",{ref:M,args:[d,b,T],position:g,rotation:h,scale:y,quaternion:v,matrix:P,matrixAutoUpdate:j}))}));function s({frames:e,renderPriority:r,children:t,camera:a}){let u=0;return n.useFrame((r=>{(e===1/0||u<e)&&(a.current.update(r.gl,r.scene),u++)}),r),c.createElement(c.Fragment,null,t)}exports.RenderCubeTexture=l;
