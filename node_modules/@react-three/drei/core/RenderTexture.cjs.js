"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("three"),t=require("react"),n=require("@react-three/fiber"),a=require("./Fbo.cjs.js");function l(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var u=l(e),o=i(r),s=i(t);const c=s.forwardRef((({children:e,compute:r,width:t,height:l,samples:i=8,renderPriority:c=0,eventPriority:d=0,frames:g=1/0,stencilBuffer:p=!1,depthBuffer:m=!0,generateMipmaps:b=!1,...v},h)=>{const{size:j,viewport:x}=n.useThree(),y=a.useFBO((t||j.width)*x.dpr,(l||j.height)*x.dpr,{samples:i,stencilBuffer:p,depthBuffer:m,generateMipmaps:b}),[P]=s.useState((()=>new o.Scene)),O=s.useCallback(((e,r,t)=>{var n,a;let l=null==(n=y.texture)||null==(n=n.__r3f.parent)?void 0:n.object;for(;l&&!(l instanceof o.Object3D);){var i;l=null==(i=l.__r3f.parent)?void 0:i.object}if(!l)return!1;t.raycaster.camera||t.events.compute(e,t,null==(a=t.previousRoot)?void 0:a.getState());const[u]=t.raycaster.intersectObject(l);if(!u)return!1;const s=u.uv;if(!s)return!1;r.raycaster.setFromCamera(r.pointer.set(2*s.x-1,2*s.y-1),r.camera)}),[]);return s.useImperativeHandle(h,(()=>y.texture),[y]),s.createElement(s.Fragment,null,n.createPortal(s.createElement(f,{renderPriority:c,frames:g,fbo:y},e,s.createElement("group",{onPointerOver:()=>null})),P,{events:{compute:r||O,priority:d}}),s.createElement("primitive",u.default({object:y.texture},v)))}));function f({frames:e,renderPriority:r,children:t,fbo:a}){let l,i,u,o,c=0;return n.useFrame((r=>{(e===1/0||c<e)&&(l=r.gl.autoClear,i=r.gl.xr.enabled,u=r.gl.getRenderTarget(),o=r.gl.xr.isPresenting,r.gl.autoClear=!0,r.gl.xr.enabled=!1,r.gl.xr.isPresenting=!1,r.gl.setRenderTarget(a),r.gl.render(r.scene,r.camera),r.gl.setRenderTarget(u),r.gl.autoClear=l,r.gl.xr.enabled=i,r.gl.xr.isPresenting=o,c++)}),r),s.createElement(s.Fragment,null,t)}exports.RenderTexture=c;
