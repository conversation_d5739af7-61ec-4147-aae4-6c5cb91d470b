"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),r=require("@react-three/fiber"),t=require("react"),c=require("three"),a=require("./calculateScaleFactor.cjs.js");function u(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function n(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach((function(t){if("default"!==t){var c=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,c.get?c:{enumerable:!0,get:function(){return e[t]}})}})),r.default=e,Object.freeze(r)}var l=u(e),o=n(t);const i=new c.Vector3,s=t.forwardRef((({scale:e=1,...c},u)=>{const n=t.useRef(null);return o.useImperativeHandle(u,(()=>n.current),[]),r.useFrame((r=>{const t=n.current;if(!t)return;const c=a.calculateScaleFactor(t.getWorldPosition(i),e,r.camera,r.size);t.scale.setScalar(c*e)})),o.createElement("object3D",l.default({ref:n},c))}));exports.ScreenSizer=s;
