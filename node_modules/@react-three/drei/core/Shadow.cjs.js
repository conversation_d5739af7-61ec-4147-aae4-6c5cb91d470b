"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three");function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function o(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var a=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,a.get?a:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var n=a(e),c=o(t);const i=c.forwardRef((({fog:e=!1,renderOrder:t,depthWrite:a=!1,colorStop:o=0,color:i="black",opacity:l=.5,...d},u)=>{const f=c.useMemo((()=>{const e=document.createElement("canvas");e.width=128,e.height=128;const t=e.getContext("2d"),a=t.createRadialGradient(e.width/2,e.height/2,0,e.width/2,e.height/2,e.width/2);return a.addColorStop(o,new r.Color(i).getStyle()),a.addColorStop(1,"rgba(0,0,0,0)"),t.fillStyle=a,t.fillRect(0,0,e.width,e.height),e}),[i,o]);return c.createElement("mesh",n.default({renderOrder:t,ref:u,"rotation-x":-Math.PI/2},d),c.createElement("planeGeometry",null),c.createElement("meshBasicMaterial",{transparent:!0,opacity:l,fog:e,depthWrite:a,side:r.DoubleSide},c.createElement("canvasTexture",{attach:"map",args:[f]})))}));exports.Shadow=i;
