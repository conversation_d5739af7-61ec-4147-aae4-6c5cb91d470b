"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=require("@babel/runtime/helpers/extends"),t=require("react"),r=require("three"),i=require("@react-three/fiber"),o=require("../helpers/constants.cjs.js");function n(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function a(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=n(e),l=a(t),c=a(r);class u extends c.ShaderMaterial{constructor(){super({uniforms:{time:{value:0},pixelRatio:{value:1}},vertexShader:"\n        uniform float pixelRatio;\n        uniform float time;\n        attribute float size;  \n        attribute float speed;  \n        attribute float opacity;\n        attribute vec3 noise;\n        attribute vec3 color;\n        varying vec3 vColor;\n        varying float vOpacity;\n\n        void main() {\n          vec4 modelPosition = modelMatrix * vec4(position, 1.0);\n          modelPosition.y += sin(time * speed + modelPosition.x * noise.x * 100.0) * 0.2;\n          modelPosition.z += cos(time * speed + modelPosition.x * noise.y * 100.0) * 0.2;\n          modelPosition.x += cos(time * speed + modelPosition.x * noise.z * 100.0) * 0.2;\n          vec4 viewPosition = viewMatrix * modelPosition;\n          vec4 projectionPostion = projectionMatrix * viewPosition;\n          gl_Position = projectionPostion;\n          gl_PointSize = size * 25. * pixelRatio;\n          gl_PointSize *= (1.0 / - viewPosition.z);\n          vColor = color;\n          vOpacity = opacity;\n        }\n      ",fragmentShader:`\n        varying vec3 vColor;\n        varying float vOpacity;\n        void main() {\n          float distanceToCenter = distance(gl_PointCoord, vec2(0.5));\n          float strength = 0.05 / distanceToCenter - 0.1;\n          gl_FragColor = vec4(vColor, strength * vOpacity);\n          #include <tonemapping_fragment>\n          #include <${o.version>=154?"colorspace_fragment":"encodings_fragment"}>\n        }\n      `})}get time(){return this.uniforms.time.value}set time(e){this.uniforms.time.value=e}get pixelRatio(){return this.uniforms.pixelRatio.value}set pixelRatio(e){this.uniforms.pixelRatio.value=e}}const f=e=>e&&e.constructor===Float32Array,m=e=>e instanceof c.Vector2||e instanceof c.Vector3||e instanceof c.Vector4,p=e=>Array.isArray(e)?e:m(e)?e.toArray():[e,e,e];function d(e,t,r){return l.useMemo((()=>{if(void 0!==t){if(f(t))return t;if(t instanceof c.Color){const r=Array.from({length:3*e},(()=>{return[(e=t).r,e.g,e.b];var e})).flat();return Float32Array.from(r)}if(m(t)||Array.isArray(t)){const r=Array.from({length:3*e},(()=>p(t))).flat();return Float32Array.from(r)}return Float32Array.from({length:e},(()=>t))}return Float32Array.from({length:e},r)}),[t])}const v=l.forwardRef((({noise:e=1,count:t=100,speed:r=1,opacity:o=1,scale:n=1,size:a,color:m,children:v,...b},g)=>{l.useMemo((()=>i.extend({SparklesImplMaterial:u})),[]);const y=l.useRef(null),h=i.useThree((e=>e.viewport.dpr)),x=p(n),A=l.useMemo((()=>Float32Array.from(Array.from({length:t},(()=>x.map(c.MathUtils.randFloatSpread))).flat())),[t,...x]),P=d(t,a,Math.random),M=d(t,o),j=d(t,r),O=d(3*t,e),_=d(void 0===m?3*t:t,f(m)?m:new c.Color(m),(()=>1));return i.useFrame((e=>{y.current&&y.current.material&&(y.current.material.time=e.clock.elapsedTime)})),l.useImperativeHandle(g,(()=>y.current),[]),l.createElement("points",s.default({key:`particle-${t}-${JSON.stringify(n)}`},b,{ref:y}),l.createElement("bufferGeometry",null,l.createElement("bufferAttribute",{attach:"attributes-position",args:[A,3]}),l.createElement("bufferAttribute",{attach:"attributes-size",args:[P,1]}),l.createElement("bufferAttribute",{attach:"attributes-opacity",args:[M,1]}),l.createElement("bufferAttribute",{attach:"attributes-speed",args:[j,1]}),l.createElement("bufferAttribute",{attach:"attributes-color",args:[_,3]}),l.createElement("bufferAttribute",{attach:"attributes-noise",args:[O,3]})),v||l.createElement("sparklesImplMaterial",{transparent:!0,pixelRatio:h,depthWrite:!1}))}));exports.Sparkles=v;
