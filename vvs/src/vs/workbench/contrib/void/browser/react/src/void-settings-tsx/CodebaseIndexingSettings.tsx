import React, { useState, useEffect } from 'react';
import { SettingsCard, SettingsButton } from './SettingsCard.js';
import { ICodebaseIndexingService, IIndexingProgress, IIndexStats } from '../../../../common/codebaseIndexingTypes.js';
// import { getIconClasses } from '../../../../../../../platform/theme/common/iconRegistry.js'; // For file icons - REMOVED
import { basename } from '../../../../../../../base/common/path.js'; // For getting basename

interface CodebaseIndexingSettingsProps {
  codebaseIndexingService: ICodebaseIndexingService;
}

export const CodebaseIndexingSettings: React.FC<CodebaseIndexingSettingsProps> = ({ codebaseIndexingService }) => {
  const [isIndexing, setIsIndexing] = useState(false);
  const [progress, setProgress] = useState<IIndexingProgress | null>(null);
  const [stats, setStats] = useState<IIndexStats | null>(null);

  useEffect(() => {
    // Subscribe to indexing status changes
    const statusDisposable = codebaseIndexingService.onDidChangeIndexingStatus((indexing) => {
      setIsIndexing(indexing);
    });

    // Subscribe to progress changes
    const progressDisposable = codebaseIndexingService.onDidChangeProgress((progressData) => {
      setProgress(progressData);
    });

    // Get initial state
    setIsIndexing(codebaseIndexingService.isIndexing());
    setProgress(codebaseIndexingService.getProgress());

    // Load stats initially and refresh periodically
    const loadStats = async () => {
      try {
        const currentStats = await codebaseIndexingService.getIndexStats();
        setStats(currentStats);
      } catch (error) {
        console.error('Failed to load index stats:', error);
      }
    };

    loadStats();

    // Refresh stats every 10 seconds when indexing, every 30 seconds when idle
    const statsInterval = setInterval(loadStats, isIndexing ? 10000 : 30000);

    return () => {
      statusDisposable.dispose();
      progressDisposable.dispose();
      clearInterval(statsInterval);
    };
  }, [codebaseIndexingService, isIndexing]);

  const handleStartIndexing = async () => {
    try {
      await codebaseIndexingService.startIndexing();
    } catch (error) {
      console.error('Failed to start indexing:', error);
    }
  };

  const handleStopIndexing = async () => {
    try {
      await codebaseIndexingService.stopIndexing();
    } catch (error) {
      console.error('Failed to stop indexing:', error);
    }
  };

  const handleClearIndex = async () => {
    if (confirm('Are you sure you want to clear the entire codebase index? This action cannot be undone.')) {
      try {
        await codebaseIndexingService.clearIndex();
        // Refresh stats after clearing
        const newStats = await codebaseIndexingService.getIndexStats();
        setStats(newStats);
      } catch (error) {
        console.error('Failed to clear index:', error);
      }
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      dateStyle: 'medium',
      timeStyle: 'short'
    }).format(date);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const getStatusBadge = () => {
    if (isIndexing) {
      return <span className="text-blue-600 dark:text-blue-400">Indexing...</span>;
    }
    if (stats && stats.totalChunks > 0) { // totalChunks is now a proxy for successful indexing from Pinecone
      return <span className="text-green-600 dark:text-green-400">Indexed</span>;
    }
    if (stats && stats.totalFiles > 0 && stats.totalChunks === 0 && progress?.phase !== 'idle'){
        return <span className="text-yellow-500 dark:text-yellow-400">Partially Indexed (No Chunks)</span>;
    }
    return <span className="text-gray-500 dark:text-gray-400">Not Indexed</span>;
  };

  const getFileIconClass = (filePath: string) => {
    const extension = filePath.split('.').pop()?.toLowerCase() || '';
    // Simple mapping for a few common file types to Codicons
    switch (extension) {
      case 'ts':
      case 'tsx':
        return 'codicon codicon-file-code';
      case 'js':
      case 'jsx':
        return 'codicon codicon-file-code';
      case 'py':
        return 'codicon codicon-file-code';
      case 'json':
        return 'codicon codicon-json';
      case 'md':
        return 'codicon codicon-markdown';
      case 'css':
      case 'scss':
      case 'less':
        return 'codicon codicon-file-code';
      case 'html':
        return 'codicon codicon-file-code';
      default:
        return 'codicon codicon-file';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'parsing':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'parsed':
        return 'bg-blue-500/20 text-blue-400';
      case 'embedding':
        return 'bg-purple-500/20 text-purple-400';
      case 'indexed':
        return 'bg-green-500/20 text-green-400';
      case 'error':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-void-bg-3 text-void-fg-3';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'parsing':
        return 'Parsing';
      case 'parsed':
        return 'Parsed';
      case 'embedding':
        return 'Embedding';
      case 'indexed':
        return 'Indexed';
      case 'error':
        return 'Error';
      default:
        return status;
    }
  };

  // Use the percentage directly from the new progress structure
  const displayProgress = (() => {
    if (!isIndexing && stats && stats.totalChunks > 0) {
      // When not indexing, show 100% if we have vectors
      return 100;
    }

    if (progress && progress.type === 'progress') {
      return progress.percentage;
    }

    return 0;
  })();

  return (
    <SettingsCard
      title="Codebase Indexing"
      description="Index your codebase for AI-powered semantic search"
    >
      <div className="space-y-4">
        {/* Status Section */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
            <span className={`text-sm ${isIndexing ? 'text-blue-500' : 'text-void-fg-3'}`}>
              {isIndexing ? 'Indexing...' : 'Idle'}
            </span>
          </div>

          <div className="flex gap-2">
            {!isIndexing ? (
              <SettingsButton
                onClick={handleStartIndexing}
                primary
                icon={
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  </svg>
                }
              >
                {stats && stats.totalChunks > 0 ? 'Re-index' : 'Start Indexing'}
              </SettingsButton>
            ) : (
              <SettingsButton
                onClick={handleStopIndexing}
                icon={
                  <svg className="w-4 h-4 mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                }
              >
                Stop Indexing
              </SettingsButton>
            )}
          </div>
        </div>

        {/* Progress Section */}
        {(isIndexing || (progress && progress.percentage > 0)) && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>{progress?.statusText || 'Processing...'}</span>
              <span>{Math.round(displayProgress)}%</span>
            </div>

            <div className="w-full bg-void-bg-3 rounded-full h-2">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(100, Math.max(0, displayProgress))}%` }}
              />
            </div>

            <div className="text-xs text-void-fg-3">
              {progress?.phase === 'parsing' ? (
                <>Files parsed: {progress?.filesParsedCount || 0} / {progress?.totalFilesToProcess || 0}</>
              ) : progress?.phase === 'embedding' ? (
                <>
                  Files indexed: {progress?.filesEmbeddedSuccessfullyCount || 0} / {progress?.totalFilesToProcess || 0}
                  {progress?.completedChunks > 0 && (
                    <> | Vectors: {progress.completedChunks} / {progress.totalChunks}</>
                  )}
                </>
              ) : (
                <>Files: {progress?.filesEmbeddedSuccessfullyCount || 0} / {progress?.totalFilesToProcess || 0}</>
              )}
              {progress?.filesWithEmbeddingsErrorsCount > 0 && (
                <> | Errors: {progress.filesWithEmbeddingsErrorsCount}</>
              )}
            </div>
          </div>
        )}

        {/* Recent Activity */}
        {progress?.recentlyProcessedFiles && progress.recentlyProcessedFiles.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Recent Activity:</h4>
            <div className="space-y-1 max-h-48 overflow-y-auto">
              {progress.recentlyProcessedFiles.map((file, index) => (
                <div key={`${file.filePath}-${index}`} className="flex items-center gap-2 text-xs p-2 bg-void-bg-2 rounded">
                  <i className={getFileIconClass(file.fileName)} />
                  <div className="flex-1 min-w-0">
                    <div className="truncate" title={file.filePath}>
                      {file.fileName}
                    </div>
                    {file.errorDetails && (
                      <div className="text-red-400 text-xs truncate" title={file.errorDetails}>
                        {file.errorDetails}
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col items-end gap-1">
                    <span className={`px-2 py-0.5 rounded text-xs font-medium ${getStatusColor(file.status)}`}>
                      {getStatusText(file.status)}
                    </span>
                    <span className="text-xs text-void-fg-3">
                      {file.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Stats Section */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Index Statistics:</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-void-fg-3">Files indexed:</span>
              <span className="ml-2 font-medium">{stats?.totalFiles || 0}</span>
            </div>
            <div>
              <span className="text-void-fg-3">Vectors stored:</span>
              <span className="ml-2 font-medium">{stats?.totalChunks || 0}</span>
            </div>
          </div>

          {stats?.lastUpdated && (
            <div className="text-xs text-void-fg-3">
              Last updated: {stats.lastUpdated.toLocaleString()}
            </div>
          )}
        </div>

        {/* Actions */}
        {stats && stats.totalChunks > 0 && (
          <div className="pt-4 border-t border-void-border-3">
            <SettingsButton
              onClick={handleClearIndex}
              disabled={isIndexing}
              icon={
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              }
            >
              Clear Index
            </SettingsButton>
          </div>
        )}

        {/* Help Text */}
        <div className="text-xs text-void-fg-3 space-y-1">
          <p>Indexing parses your code and generates embeddings for semantic search.</p>
          <p>Supported languages: TypeScript, JavaScript, Python, Java, Go, Rust, and more.</p>
        </div>
      </div>
    </SettingsCard>
  );
};